# Email Configuration Setup

This guide will help you configure email notifications for the Tickets application using Gmail.

## Prerequisites

1. A Gmail account (<EMAIL>)
2. 2-Factor Authentication enabled on the Gmail account

## Step 1: Enable 2-Factor Authentication

1. Go to your Google Account settings: https://myaccount.google.com/
2. Navigate to "Security" in the left sidebar
3. Under "Signing in to Google", click on "2-Step Verification"
4. Follow the prompts to enable 2-Factor Authentication if not already enabled

## Step 2: Generate an App Password

1. Go to your Google Account settings: https://myaccount.google.com/
2. Navigate to "Security" in the left sidebar
3. Under "Signing in to Google", click on "App passwords"
4. You may need to sign in again
5. Select "Mail" for the app and "Other (custom name)" for the device
6. Enter "Tickets Application" as the custom name
7. Click "Generate"
8. Copy the 16-character app password (it will look like: `abcd efgh ijkl mnop`)

## Step 3: Configure Rails Credentials

### Option A: Using Rails Credentials (Recommended)

1. Edit the Rails credentials file:
   ```bash
   EDITOR=nano bin/rails credentials:edit
   ```

2. Add or update the SMTP section:
   ```yaml
   smtp:
     user_name: <EMAIL>
     password: your-16-character-app-password-here
   ```

3. Save and exit the editor

### Option B: Using Environment Variables (Alternative)

If you prefer to use environment variables, you can set:

```bash
export SMTP_USER_NAME=<EMAIL>
export SMTP_PASSWORD=your-16-character-app-password-here
```

Or add them to your `.env` file (if using dotenv gem):

```
SMTP_USER_NAME=<EMAIL>
SMTP_PASSWORD=your-16-character-app-password-here
```

## Step 4: Test the Configuration

1. Start your Rails server:
   ```bash
   bin/rails server
   ```

2. Create a ticket update that mentions a user (e.g., "Hey @username, check this out!")

3. Check the Rails logs to see if the email was sent successfully

## Development Mode Fallback

If SMTP credentials are not configured, the application will automatically fall back to saving emails as files in the `tmp/mails/` directory. This allows you to develop and test the email functionality without needing to configure SMTP.

## Troubleshooting

### "Username and Password not accepted" Error

- Make sure you're using an App Password, not your regular Gmail password
- Verify that 2-Factor Authentication is enabled on your Gmail account
- Double-check that the app password is entered correctly (no spaces)

### "Connection refused" Error

- Check your internet connection
- Verify that Gmail SMTP is accessible from your network
- Some corporate networks may block SMTP connections

### Emails not being sent

- Check the Rails logs for error messages
- Verify that the credentials are properly configured
- Test with a simple email first

## Security Notes

- Never commit your app password to version control
- Use Rails credentials or environment variables to store sensitive information
- Regularly rotate your app passwords
- Consider using a dedicated email service like SendGrid or Mailgun for production applications
