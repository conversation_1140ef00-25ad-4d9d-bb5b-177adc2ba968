require "test_helper"

class MentionServiceTest < ActiveSupport::TestCase
  include ActionMailer::TestHelper
  setup do
    # Clear existing users to avoid unique constraint violations
    User.delete_all

    # Create test users
    @user1 = User.create!(
      email: "<EMAIL>",
      username: "user1",
      password: "password123",
      password_confirmation: "password123"
    )

    @user2 = User.create!(
      email: "<EMAIL>",
      username: "user2",
      password: "password123",
      password_confirmation: "password123"
    )

    @user3 = User.create!(
      email: "<EMAIL>",
      username: "user3",
      password: "password123",
      password_confirmation: "password123"
    )

    # Create a test ticket
    @ticket = Ticket.create!(
      title: "Test ticket for mentions",
      description: "This is a test ticket",
      status: "todo",
      priority: "medium",
      points: 3
    )
  end

  test "extract_mentions finds mentioned users" do
    # Test with a single mention
    text = "This is a test update mentioning @user1"
    mentioned_users = MentionService.extract_mentions(text)

    assert_equal 1, mentioned_users.count
    assert_equal @user1.id, mentioned_users.first.id

    # Test with multiple mentions
    text = "Hey @user1 and @user2, please check this out"
    mentioned_users = MentionService.extract_mentions(text)

    assert_equal 2, mentioned_users.count
    assert_includes mentioned_users.map(&:id), @user1.id
    assert_includes mentioned_users.map(&:id), @user2.id

    # Test with duplicate mentions
    text = "Hey @user1 and @user1 again"
    mentioned_users = MentionService.extract_mentions(text)

    assert_equal 1, mentioned_users.count
    assert_equal @user1.id, mentioned_users.first.id

    # Test with non-existent user
    text = "Hey @nonexistentuser, are you there?"
    mentioned_users = MentionService.extract_mentions(text)

    assert_equal 0, mentioned_users.count
  end

  test "notify_mentioned_users sends emails to mentioned users" do
    # Create an update with mentions
    update = @ticket.updates.create!(
      content: "Hey @user1 and @user2, please review this ticket",
      user: @user3
    )

    # Ensure emails are being sent
    assert_emails 2 do
      mentioned_users = MentionService.notify_mentioned_users(update)

      assert_equal 2, mentioned_users.count
      assert_includes mentioned_users.map(&:id), @user1.id
      assert_includes mentioned_users.map(&:id), @user2.id
    end
  end

  test "notify_mentioned_users does not send email to the update author" do
    # Create an update where the author mentions themselves
    update = @ticket.updates.create!(
      content: "I (@user1) am working on this ticket",
      user: @user1
    )

    # Ensure no emails are sent (since the only mention is the author)
    assert_no_emails do
      mentioned_users = MentionService.notify_mentioned_users(update)
      assert_equal 0, mentioned_users.count
    end

    # Create an update where the author mentions themselves and others
    update = @ticket.updates.create!(
      content: "I (@user1) need help from @user2",
      user: @user1
    )

    # Ensure only one email is sent (to user2, not to the author user1)
    assert_emails 1 do
      mentioned_users = MentionService.notify_mentioned_users(update)
      assert_equal 1, mentioned_users.count
      assert_equal @user2.id, mentioned_users.first.id
    end
  end
end
