require "test_helper"

class TicketTest < ActiveSupport::TestCase
  setup do
    # Clear existing users to avoid unique constraint violations
    User.delete_all
  end

  test "can assign multiple users to a ticket" do
    ticket = Ticket.create!(
      title: "Test ticket with multiple assignees",
      description: "This is a test ticket",
      status: "todo",
      priority: "medium",
      points: 3
    )

    user1 = User.create!(
      email: "<EMAIL>",
      username: "test_user1",
      password: "password123",
      password_confirmation: "password123"
    )

    user2 = User.create!(
      email: "<EMAIL>",
      username: "test_user2",
      password: "password123",
      password_confirmation: "password123"
    )

    # Assign multiple users to the ticket
    ticket.users << user1
    ticket.users << user2

    # Reload the ticket to ensure associations are saved
    ticket.reload

    # Verify that the ticket has multiple users assigned
    assert_equal 2, ticket.users.count
    assert_includes ticket.users, user1
    assert_includes ticket.users, user2

    # Test helper methods
    assert ticket.assigned?
    assert_equal "#{user1.display_name}, #{user2.display_name}", ticket.assignee_names
  end

  test "ticket can have no users assigned" do
    ticket = Ticket.create!(
      title: "Unassigned test ticket",
      description: "This is an unassigned test ticket",
      status: "todo",
      priority: "medium",
      points: 3
    )

    assert_equal 0, ticket.users.count
    assert_not ticket.assigned?
    assert_equal "", ticket.assignee_names
  end
end
