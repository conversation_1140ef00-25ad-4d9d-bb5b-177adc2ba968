require "test_helper"

class MentionHelperTest < ActionView::TestCase
  include Mention<PERSON><PERSON>per

  def setup
    @user1 = User.create!(username: 'testuser1', email: '<EMAIL>', password: 'password123')
    @user2 = User.create!(username: 'testuser2', email: '<EMAIL>', password: 'password123')
  end

  test "process_mentions highlights existing users" do
    text = "Hey @testuser1, can you help @testuser2 with this?"
    result = process_mentions(text)
    
    # Should contain styled mentions
    assert_includes result, 'class="user-mention"'
    assert_includes result, '@testuser1'
    assert_includes result, '@testuser2'
    assert_includes result, 'title="testuser1 (<EMAIL>)"'
  end

  test "process_mentions ignores non-existent users" do
    text = "Hey @nonexistentuser, how are you?"
    result = process_mentions(text)
    
    # Should not style non-existent users
    assert_not_includes result, 'class="user-mention"'
    assert_includes result, '@nonexistentuser'
  end

  test "process_mentions handles empty text" do
    assert_equal '', process_mentions('')
    assert_equal '', process_mentions(nil)
  end

  test "process_mentions preserves line breaks" do
    text = "Line 1\n@testuser1\nLine 3"
    result = process_mentions(text)
    
    # Should contain paragraph tags from simple_format
    assert_includes result, '<p>'
    assert_includes result, 'class="user-mention"'
  end

  test "process_mentions escapes HTML" do
    text = "Hey @testuser1, check this <script>alert('xss')</script>"
    result = process_mentions(text)
    
    # Should escape the script tag
    assert_includes result, '&lt;script&gt;'
    assert_not_includes result, '<script>'
    assert_includes result, 'class="user-mention"'
  end

  test "extract_mentioned_users returns correct users" do
    text = "Hey @testuser1 and @testuser2, please review this"
    users = extract_mentioned_users(text)
    
    assert_equal 2, users.count
    assert_includes users, @user1
    assert_includes users, @user2
  end

  test "extract_mentioned_users handles non-existent users" do
    text = "Hey @testuser1 and @nonexistentuser"
    users = extract_mentioned_users(text)
    
    assert_equal 1, users.count
    assert_includes users, @user1
  end

  test "current_user_mentioned? detects mentions correctly" do
    text = "Hey @testuser1, can you help?"
    
    assert current_user_mentioned?(text, @user1)
    assert_not current_user_mentioned?(text, @user2)
    assert_not current_user_mentioned?(text, nil)
  end
end
