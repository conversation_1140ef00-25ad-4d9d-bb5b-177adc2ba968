require "test_helper"

class NotificationMailerTest < ActionMailer::TestCase
  setup do
    # Clear existing users to avoid unique constraint violations
    User.delete_all
    
    # Create test users
    @mentioned_user = User.create!(
      email: "<EMAIL>",
      username: "mentioned_user",
      password: "password123",
      password_confirmation: "password123"
    )
    
    @mentioner = User.create!(
      email: "<EMAIL>",
      username: "mentioner",
      password: "password123",
      password_confirmation: "password123"
    )
    
    # Create a test ticket
    @ticket = Ticket.create!(
      title: "Test ticket for mention notifications",
      description: "This is a test ticket",
      status: "todo",
      priority: "medium",
      points: 3
    )
    
    # Create a test update
    @update = @ticket.updates.create!(
      content: "Hey @mentioned_user, please check this out!",
      user: @mentioner
    )
  end
  
  test "mention_notification" do
    # Generate the email
    email = NotificationMailer.mention_notification(@mentioned_user, @update)
    
    # Test the email properties
    assert_emails 1 do
      email.deliver_now
    end
    
    # Check email content
    assert_equal ["<EMAIL>"], email.from
    assert_equal [@mentioned_user.email], email.to
    assert_equal "You were mentioned in Ticket ##{@ticket.id}: #{@ticket.title}", email.subject
    
    # Check that the email body contains the update content
    assert_match @update.content, email.html_part.body.to_s
    assert_match @update.content, email.text_part.body.to_s
    
    # Check that the email mentions the ticket and users
    assert_match @ticket.title, email.html_part.body.to_s
    assert_match @mentioner.display_name, email.html_part.body.to_s
    assert_match @mentioned_user.display_name, email.html_part.body.to_s
  end
end
