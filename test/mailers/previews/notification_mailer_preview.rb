# Preview all emails at http://localhost:3000/rails/mailers/notification_mailer
class NotificationMailerPreview < ActionMailer::Preview
  def mention_notification
    # Find or create a user to be mentioned
    user = User.first || User.create!(
      email: "<EMAIL>",
      username: "preview_user",
      password: "password123",
      password_confirmation: "password123"
    )
    
    # Find or create a user who is mentioning
    mentioner = User.second || User.create!(
      email: "<EMAIL>",
      username: "preview_mentioner",
      password: "password123",
      password_confirmation: "password123"
    )
    
    # Find or create a ticket
    ticket = Ticket.first || Ticket.create!(
      title: "Preview ticket for mention notifications",
      description: "This is a preview ticket",
      status: "todo",
      priority: "medium",
      points: 3
    )
    
    # Create a test update
    update = ticket.updates.create!(
      content: "Hey @#{user.username}, please check this out!\nThis is a multi-line update\nwith some code examples:\n\n```ruby\ndef hello\n  puts 'Hello, world!'\nend\n```",
      user: mentioner
    )
    
    NotificationMailer.mention_notification(user, update)
  end
end
