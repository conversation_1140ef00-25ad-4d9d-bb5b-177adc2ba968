require "test_helper"

class UpdatesControllerTest < ActionDispatch::IntegrationTest
  include Devise::Test::IntegrationHelpers
  include ActionMailer::TestHelper

  setup do
    # Clear existing users to avoid unique constraint violations
    User.delete_all

    # Create test users
    @user1 = User.create!(
      email: "<EMAIL>",
      username: "user1",
      password: "password123",
      password_confirmation: "password123"
    )

    @user2 = User.create!(
      email: "<EMAIL>",
      username: "user2",
      password: "password123",
      password_confirmation: "password123"
    )

    # Create a test ticket
    @ticket = Ticket.create!(
      title: "Test ticket for updates",
      description: "This is a test ticket",
      status: "todo",
      priority: "medium",
      points: 3
    )
  end

  def login_as_user1
    post user_session_path, params: {
      user: {
        email: @user1.email,
        password: "password123"
      }
    }
  end

  test "should create update without mentions" do
    login_as_user1

    assert_difference("Update.count") do
      post ticket_updates_url(@ticket), params: {
        update: { content: "This is a test update with no mentions" }
      }
    end

    assert_redirected_to ticket_url(@ticket)
    assert_equal "Update was successfully added.", flash[:notice]
  end

  test "should create update with mentions and send notifications" do
    login_as_user1

    assert_difference("Update.count") do
      assert_emails 1 do
        post ticket_updates_url(@ticket), params: {
          update: { content: "This is a test update mentioning @user2" }
        }
      end
    end

    assert_redirected_to ticket_url(@ticket)
    assert_match "Notifications sent to user2", flash[:notice]
  end

  test "should not send notification when mentioning self" do
    login_as_user1

    assert_difference("Update.count") do
      assert_no_emails do
        post ticket_updates_url(@ticket), params: {
          update: { content: "This is a test update mentioning myself @user1" }
        }
      end
    end

    assert_redirected_to ticket_url(@ticket)
    assert_equal "Update was successfully added.", flash[:notice]
  end
end
