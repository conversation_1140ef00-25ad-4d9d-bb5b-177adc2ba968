namespace :email do
  desc "Test email configuration by sending a test email"
  task test: :environment do
    puts "Testing email configuration..."
    
    # Check if we have SMTP credentials
    smtp_user = Rails.application.credentials.dig(:smtp, :user_name) || ENV['SMTP_USER_NAME']
    smtp_password = Rails.application.credentials.dig(:smtp, :password) || ENV['SMTP_PASSWORD']
    
    if smtp_user && smtp_password
      puts "✅ SMTP credentials found"
      puts "   User: #{smtp_user}"
      puts "   Password: #{'*' * smtp_password.length}"
    else
      puts "❌ SMTP credentials not found"
      puts "   Please configure SMTP credentials in Rails credentials or environment variables"
      puts "   See EMAIL_SETUP.md for instructions"
      exit 1
    end
    
    # Find a user to send test email to
    user = User.first
    unless user
      puts "❌ No users found in database"
      puts "   Please create a user first"
      exit 1
    end
    
    puts "📧 Sending test email to: #{user.email}"
    
    begin
      # Create a test ticket and update
      ticket = Ticket.first || Ticket.create!(
        title: "Test ticket for email notifications",
        description: "This is a test ticket",
        status: "todo",
        priority: "medium",
        points: 1
      )
      
      update = ticket.updates.create!(
        content: "This is a test update mentioning @#{user.username}",
        user: user
      )
      
      # Send the notification
      NotificationMailer.mention_notification(user, update).deliver_now
      
      puts "✅ Test email sent successfully!"
      puts "   Check the recipient's inbox: #{user.email}"
      
    rescue => e
      puts "❌ Failed to send test email:"
      puts "   Error: #{e.class}: #{e.message}"
      puts "   Please check your SMTP configuration"
      
      if e.message.include?("Username and Password not accepted")
        puts ""
        puts "💡 This error usually means:"
        puts "   - You're using your regular Gmail password instead of an App Password"
        puts "   - 2-Factor Authentication is not enabled on your Gmail account"
        puts "   - The App Password is incorrect"
        puts ""
        puts "   See EMAIL_SETUP.md for detailed setup instructions"
      end
    end
  end
  
  desc "Show current email configuration"
  task config: :environment do
    puts "Current email configuration:"
    puts "  Delivery method: #{ActionMailer::Base.delivery_method}"
    
    if ActionMailer::Base.delivery_method == :smtp
      settings = ActionMailer::Base.smtp_settings
      puts "  SMTP settings:"
      puts "    Address: #{settings[:address]}"
      puts "    Port: #{settings[:port]}"
      puts "    Domain: #{settings[:domain]}"
      puts "    User name: #{settings[:user_name]}"
      puts "    Password: #{'*' * (settings[:password]&.length || 0)}"
      puts "    Authentication: #{settings[:authentication]}"
      puts "    Enable STARTTLS: #{settings[:enable_starttls_auto]}"
    elsif ActionMailer::Base.delivery_method == :file
      puts "  File delivery location: #{ActionMailer::Base.file_settings[:location]}"
    end
    
    puts "  Default from address: #{ApplicationMailer.default[:from]}"
  end
end
