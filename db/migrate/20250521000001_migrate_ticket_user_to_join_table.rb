class MigrateTicketUserToJoinTable < ActiveRecord::Migration[8.0]
  def up
    # Copy existing user assignments to the join table
    execute <<-SQL
      INSERT INTO tickets_users (ticket_id, user_id)
      SELECT id, user_id FROM tickets
      WHERE user_id IS NOT NULL
    SQL
    
    # Remove the user_id column from tickets
    remove_reference :tickets, :user
  end

  def down
    # Add the user_id column back to tickets
    add_reference :tickets, :user, null: true, foreign_key: true
    
    # For each ticket, assign the first user from the join table (if any)
    execute <<-SQL
      UPDATE tickets
      SET user_id = (
        SELECT user_id
        FROM tickets_users
        WHERE tickets_users.ticket_id = tickets.id
        LIMIT 1
      )
    SQL
    
    # We can't fully restore the original state if a ticket had multiple users
  end
end
