# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_05_21_000001) do
  create_table "labels", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "labels_tickets", id: false, force: :cascade do |t|
    t.integer "ticket_id", null: false
    t.integer "label_id", null: false
    t.index ["label_id", "ticket_id"], name: "index_labels_tickets_on_label_id_and_ticket_id"
    t.index ["ticket_id", "label_id"], name: "index_labels_tickets_on_ticket_id_and_label_id"
  end

  create_table "tickets", force: :cascade do |t|
    t.string "title"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "status", default: "none"
    t.string "priority", default: "medium"
    t.integer "points", default: 0
    t.index ["priority"], name: "index_tickets_on_priority"
    t.index ["status"], name: "index_tickets_on_status"
  end

  create_table "tickets_users", id: false, force: :cascade do |t|
    t.integer "ticket_id", null: false
    t.integer "user_id", null: false
    t.index ["ticket_id", "user_id"], name: "index_tickets_users_on_ticket_id_and_user_id"
    t.index ["user_id", "ticket_id"], name: "index_tickets_users_on_user_id_and_ticket_id"
  end

  create_table "updates", force: :cascade do |t|
    t.text "content"
    t.integer "ticket_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "user_id"
    t.index ["ticket_id"], name: "index_updates_on_ticket_id"
    t.index ["user_id"], name: "index_updates_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.string "username"
    t.string "picture"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  add_foreign_key "updates", "tickets"
  add_foreign_key "updates", "users"
end
