require "active_support/core_ext/integer/time"

Rails.application.configure do
  # Settings specified here will take precedence over those in config/application.rb.

  # Make code changes take effect immediately without server restart.
  config.enable_reloading = true

  # Do not eager load code on boot.
  config.eager_load = false

  # Show full error reports.
  config.consider_all_requests_local = true

  # Enable server timing.
  config.server_timing = true

  # Enable/disable Action Controller caching. By default Action Controller caching is disabled.
  # Run rails dev:cache to toggle Action Controller caching.
  if Rails.root.join("tmp/caching-dev.txt").exist?
    config.action_controller.perform_caching = true
    config.action_controller.enable_fragment_cache_logging = true
    config.public_file_server.headers = { "cache-control" => "public, max-age=#{2.days.to_i}" }
  else
    config.action_controller.perform_caching = false
  end

  # Change to :null_store to avoid any caching.
  config.cache_store = :memory_store

  # Store uploaded files on the local file system (see config/storage.yml for options).
  config.active_storage.service = :local

  # Enable email delivery errors for debugging in development
  config.action_mailer.raise_delivery_errors = true
  config.action_mailer.delivery_method = :smtp

  # Make template changes take effect immediately.
  config.action_mailer.perform_caching = false

  # Set localhost to be used by links generated in mailer templates.
  config.action_mailer.default_url_options = { host: "localhost", port: 3000 }

  # Configure SMTP settings for Gmail
  # Check if we have SMTP credentials configured
  smtp_user = Rails.application.credentials.dig(:smtp, :user_name) || ENV['SMTP_USER_NAME']
  smtp_password = Rails.application.credentials.dig(:smtp, :password) || ENV['SMTP_PASSWORD']

  # Check if we have a valid Gmail App Password
  # Skip SMTP if the password is obviously a placeholder
  placeholder_passwords = ['your-gmail-app-password-here', 'Xoh57287', 'my-smtp-password']
  valid_app_password = smtp_password &&
                      smtp_password.length >= 16 &&
                      !placeholder_passwords.include?(smtp_password)

  if smtp_user && valid_app_password
    puts "📧 Using Gmail SMTP for email delivery"
    config.action_mailer.delivery_method = :smtp
    config.action_mailer.smtp_settings = {
      address: 'smtp.gmail.com',
      port: 587,
      domain: 'gmail.com',
      user_name: smtp_user,
      password: smtp_password,
      authentication: :plain,
      enable_starttls_auto: true
    }
  else
    # Fall back to file delivery in development if no valid SMTP credentials
    puts "⚠️  Valid Gmail App Password not configured. Emails will be saved to tmp/mails/"
    puts "   See EMAIL_SETUP.md for setup instructions"
    config.action_mailer.delivery_method = :file
    config.action_mailer.file_settings = { location: Rails.root.join('tmp', 'mails') }
  end

  # Print deprecation notices to the Rails logger.
  config.active_support.deprecation = :log

  # Raise an error on page load if there are pending migrations.
  config.active_record.migration_error = :page_load

  # Highlight code that triggered database queries in logs.
  config.active_record.verbose_query_logs = true

  # Append comments with runtime information tags to SQL queries in logs.
  config.active_record.query_log_tags_enabled = true

  # Highlight code that enqueued background job in logs.
  config.active_job.verbose_enqueue_logs = true

  # Raises error for missing translations.
  # config.i18n.raise_on_missing_translations = true

  # Annotate rendered view with file names.
  config.action_view.annotate_rendered_view_with_filenames = true

  # Uncomment if you wish to allow Action Cable access from any origin.
  # config.action_cable.disable_request_forgery_protection = true

  # Raise error when a before_action's only/except options reference missing actions.
  config.action_controller.raise_on_missing_callback_actions = true

  # Apply autocorrection by RuboCop to files generated by `bin/rails generate`.
  # config.generators.apply_rubocop_autocorrect_after_generate!
end
