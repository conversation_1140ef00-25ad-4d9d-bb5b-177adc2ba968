Rails.application.routes.draw do
  devise_for :users
  resources :users, only: [:index]
  resources :labels
  resources :tickets do
    resources :updates, only: [:create]
  end

  # Terminal help page
  get 'help' => 'pages#help', as: :help

  root "tickets#index"

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker
end
