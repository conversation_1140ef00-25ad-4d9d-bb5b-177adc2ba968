<div id="<%= dom_id ticket %>" class="modern-ticket-card">
  <div class="ticket-header">
    <div class="ticket-id">Ticket #<%= ticket.id %></div>
    <div class="terminal-command">
      <span class="terminal-prompt">tickets$</span> cat ticket_<%= ticket.id %>.txt
    </div>
  </div>

  <div class="ticket-content">
    <div class="ticket-section">
      <div style="display: flex; justify-content: space-between; align-items: center;">
        <h2 class="ticket-title"><%= ticket.title %></h2>
        <div class="ticket-meta">
          <% status_class = "status-" + ticket.status.downcase.gsub(' ', '-') %>
          <span class="ticket-status <%= status_class %>"><%= ticket.status %></span>

          <% priority_class = "priority-" + ticket.priority.downcase %>
          <span class="ticket-priority <%= priority_class %>"><%= ticket.priority %></span>

          <span class="ticket-points" title="Story Points">
            <%= ticket.points_display %>
          </span>

          <span class="ticket-assignee-indicator">
            <i class="fas fa-users"></i>
          </span>
        </div>
      </div>
    </div>

    <div class="ticket-section">
      <h3 class="section-title">Description</h3>
      <div class="ticket-description">
        <%= simple_format(ticket.description) %>
      </div>
    </div>

    <div class="ticket-section">
      <h3 class="section-title">Assignees</h3>
      <div class="ticket-assignees">
        <% if ticket.assigned? %>
          <div class="assignee-list">
            <% ticket.users.each do |user| %>
              <div class="assignee-item">
                <i class="fas fa-user"></i> <%= user.display_name %>
              </div>
            <% end %>
          </div>
        <% else %>
          <span class="no-assignees">Unassigned</span>
        <% end %>
      </div>
    </div>

    <div class="ticket-section">
      <h3 class="section-title">Labels</h3>
      <div class="ticket-labels">
        <% if ticket.labels.any? %>
          <% ticket.labels.each do |label| %>
            <span class="terminal-label"><%= label.name %></span>
          <% end %>
        <% else %>
          <span class="no-labels">None</span>
        <% end %>
      </div>
    </div>

    <div class="ticket-section">
      <h3 class="section-title">Updates</h3>
      <div class="ticket-updates">
        <% if ticket.updates.any? %>
          <div><%= pluralize(ticket.updates.count, 'update') %></div>
          <% latest_update = ticket.updates.first %>
          <% if latest_update && latest_update.created_at %>
            <div class="latest-update">
              Latest: <%= latest_update.created_at.strftime("%Y-%m-%d %H:%M") %> by <%= latest_update.user&.display_name || 'Unknown' %>
            </div>
          <% end %>
        <% else %>
          <span class="no-updates">None</span>
        <% end %>
      </div>
    </div>
  </div>
</div>
