<%= terminal_form_for(ticket) do |form| %>
  <% if ticket.errors.any? %>
    <div class="terminal-alert terminal-alert-error">
      <div>[ERROR] <%= pluralize(ticket.errors.count, "error") %> prohibited this ticket from being saved:</div>

      <ul>
        <% ticket.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="terminal-form-group">
    <%= form.label :title %>
    <%= form.text_field :title %>
  </div>

  <div class="terminal-form-group">
    <%= form.label :description %>
    <%= form.text_area :description, rows: 6 %>
  </div>

  <div class="terminal-form-row">
    <div class="terminal-form-group">
      <%= form.label :status %>
      <%= form.select :status, Ticket::STATUSES.values, {}, class: "terminal-select" %>
    </div>

    <div class="terminal-form-group">
      <%= form.label :priority %>
      <%= form.select :priority, Ticket::PRIORITIES.values, {}, class: "terminal-select" %>
    </div>

    <div class="terminal-form-group">
      <%= form.label :points, "Story Points" %>
      <%= form.select :points,
                     options_for_select(Ticket::VALID_POINTS.map { |p| [p.zero? ? "None" : p, p] }, ticket.points),
                     {},
                     class: "terminal-select" %>
    </div>
  </div>

  <div class="terminal-form-group">
    <%= form.label :user_ids, "Assignees" %>
    <div class="select2-container-wrapper">
      <%= form.collection_select :user_ids, User.all, :id, :display_name,
                               { include_blank: true },
                               { multiple: true, class: "user-select2" } %>
    </div>
    <small class="form-text text-muted">You can search by typing a name. Select multiple users by clicking on them.</small>
  </div>

  <div class="terminal-form-group">
    <%= form.label :labels %>
    <div class="terminal-checkboxes">
      <%= form.collection_check_boxes :label_ids, Label.all, :id, :name do |b| %>
        <div class="terminal-checkbox">
          <%= b.check_box %>
          <%= b.label %>
        </div>
      <% end %>
    </div>
  </div>

  <div class="terminal-form-actions">
    <%= terminal_submit_button(form) %>
    <%= terminal_back_button(tickets_path) %>
  </div>
<% end %>
