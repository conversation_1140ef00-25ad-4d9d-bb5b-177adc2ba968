<% content_for :title, "Ticket ##{@ticket.id}" %>

<%= render @ticket %>

<div class="modern-ticket-card">
  <div class="ticket-header">
    <div class="ticket-id">Ticket Actions</div>
    <div class="terminal-command">
      <span class="terminal-prompt">tickets$</span> ls -la ticket_<%= @ticket.id %>_actions
    </div>
  </div>

  <div class="ticket-content">
    <div class="ticket-actions">
      <%= link_to "Edit Ticket", edit_ticket_path(@ticket), class: "terminal-btn" %>
      <%= link_to "Back to Tickets", tickets_path, class: "terminal-btn" %>
      <%= button_to "Delete Ticket", @ticket, method: :delete,
                data: { turbo_confirm: "Are you sure you want to delete this ticket?" },
                class: "terminal-btn terminal-btn-danger" %>
    </div>
  </div>
</div>

<div class="modern-ticket-card">
  <div class="ticket-header">
    <div class="ticket-id">Updates History</div>
    <div class="terminal-command">
      <span class="terminal-prompt">tickets$</span> cat ticket_<%= @ticket.id %>_updates.log
    </div>
  </div>

  <div class="ticket-content">
    <% if @updates.any? %>
      <div class="terminal-updates">
        <% @updates.each do |update| %>
          <div class="update-item">
            <div class="update-header">
              <div class="update-user"><%= update.user&.display_name || 'unknown' %>@tickets:~$</div>
              <div class="update-time"><%= update.created_at&.strftime("%Y-%m-%d %H:%M:%S") %></div>
            </div>
            <div class="update-content">
              <%= simple_format(update.content) %>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="no-updates">
        File is empty. No updates yet.
      </div>
    <% end %>
  </div>
</div>

<div class="modern-ticket-card">
  <div class="ticket-header">
    <div class="ticket-id">Add Update</div>
    <div class="terminal-command">
      <span class="terminal-prompt">tickets$</span> tail -f ticket_<%= @ticket.id %>_updates.log
    </div>
  </div>

  <div class="ticket-content">
    <% if user_signed_in? %>
      <%= form_with(model: [@ticket, @update], local: true, class: "terminal-form") do |form| %>
        <div class="terminal-form-group">
          <p>
            User: <strong><%= current_user.display_name %></strong>@tickets:~$
          </p>
        </div>

        <div class="terminal-form-group">
          <%= form.label :content, "Update Content" %>
          <%= form.text_area :content, rows: 4, class: "terminal-input" %>
        </div>

        <div>
          <%= form.submit "Append Update", class: "terminal-btn terminal-btn-primary" %>
        </div>
      <% end %>
    <% else %>
      <div class="terminal-command">
        <span class="terminal-prompt">tickets$</span> echo "Permission denied: Please login first"
      </div>
      <p>
        Error: Please <%= link_to "login", new_user_session_path %> to add updates to this ticket.
      </p>
    <% end %>
  </div>
</div>

