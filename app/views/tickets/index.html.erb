<% content_for :title, "Tickets" %>

<%= modern_card("Ticket Actions", "ls -la") do %>
  <div class="ticket-actions">
    <%= link_to new_ticket_path, class: "terminal-btn terminal-btn-primary" do %>
      <span class="nav-icon">+</span> New Ticket
    <% end %>
    <%= link_to labels_path, class: "terminal-btn" do %>
      <span class="nav-icon">🏷️</span> Manage Labels
    <% end %>
  </div>
<% end %>

<%= modern_card("Filter Tickets", "grep --status") do %>
  <%= form_with(url: tickets_path, method: :get, class: "terminal-form filter-form") do |form| %>
    <div class="filter-form-row">
        <div class="terminal-form-group">
          <%= form.label :status, "Status:" %>
          <%= form.select :status,
                         options_for_select([["All Statuses", ""]] + Ticket::STATUSES.values.map { |s| [s, s] }, @active_status),
                         {},
                         class: "terminal-select" %>
        </div>

        <div class="terminal-form-group">
          <%= form.label :priority, "Priority:" %>
          <%= form.select :priority,
                         options_for_select([["All Priorities", ""]] + Ticket::PRIORITIES.values.map { |p| [p, p] }, @active_priority),
                         {},
                         class: "terminal-select" %>
        </div>

        <div class="terminal-form-group">
          <%= form.label :points, "Points:" %>
          <%= form.select :points,
                         options_for_select([["All Points", ""]] + Ticket::VALID_POINTS.map { |p| [p.zero? ? "None" : p, p] }, @active_points),
                         {},
                         class: "terminal-select" %>
        </div>

        <div class="terminal-form-group">
          <%= form.label :user_id, "Assignee:" %>
          <div class="select2-container-wrapper">
            <%= form.collection_select :user_id,
                                     User.all,
                                     :id,
                                     :display_name,
                                     { include_blank: "All Assignees" },
                                     { class: "user-select2-single" } %>
          </div>
          <small class="form-text text-muted">Filter by any user assigned to the ticket</small>
        </div>

        <script>
          document.addEventListener('turbo:load', function() {
            $('.user-select2-single').select2({
              theme: 'classic',
              placeholder: 'All Assignees',
              allowClear: true,
              width: '100%'
            });
          });

          document.addEventListener('turbo:render', function() {
            $('.user-select2-single').select2({
              theme: 'classic',
              placeholder: 'All Assignees',
              allowClear: true,
              width: '100%'
            });
          });
        </script>

        <div class="filter-form-actions">
          <%= form.submit "Filter", class: "terminal-btn terminal-btn-primary" %>
          <% if params[:status].present? || params[:priority].present? || params[:points].present? || params[:user_id].present? %>
            <%= link_to "Clear", tickets_path, class: "terminal-btn" %>
          <% end %>
        </div>
      </div>

      <% if @active_status.present? || @active_priority.present? || @active_points.present? || @active_user.present? %>
        <div class="active-filters">
          <div class="active-filter-label">Active filters:</div>
          <% if @active_status.present? %>
            <div class="active-filter-tag">
              <% status_class = "status-" + @active_status.downcase.gsub(' ', '-') %>
              <span class="ticket-status <%= status_class %>"><%= @active_status %></span>
            </div>
          <% end %>

          <% if @active_priority.present? %>
            <div class="active-filter-tag">
              <% priority_class = "priority-" + @active_priority.downcase %>
              <span class="ticket-priority <%= priority_class %>"><%= @active_priority %></span>
            </div>
          <% end %>

          <% if @active_points.present? %>
            <div class="active-filter-tag">
              <span class="ticket-points"><%= @active_points.zero? ? "-" : @active_points %></span>
            </div>
          <% end %>

          <% if @active_user.present? %>
            <div class="active-filter-tag">
              <span class="ticket-assignee-small">
                <i class="fas fa-users"></i> <%= @active_user.display_name %>
              </span>
            </div>
          <% end %>
        </div>
      <% end %>
  <% end %>
<% end %>

<% if @tickets.any? %>
  <%= modern_card("All Tickets", nil) do %>
    <%= modern_table do %>
      <thead>
        <tr>
          <th>ID</th>
          <th>Title</th>
          <th>Status</th>
          <th>Priority</th>
          <th>Points</th>
          <th>Assignee</th>
          <th>Labels</th>
          <th>Updates</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <% @tickets.each do |ticket| %>
          <tr>
            <td><%= ticket.id %></td>
            <td><%= ticket.title %></td>
            <td>
              <% status_class = "status-" + ticket.status.downcase.gsub(' ', '-') %>
              <span class="ticket-status <%= status_class %>"><%= ticket.status %></span>
            </td>
            <td>
              <% priority_class = "priority-" + ticket.priority.downcase %>
              <span class="ticket-priority <%= priority_class %>"><%= ticket.priority %></span>
            </td>
            <td class="text-center">
              <% if ticket.points > 0 %>
                <span class="ticket-points"><%= ticket.points %></span>
              <% else %>
                <span>-</span>
              <% end %>
            </td>
            <td>
              <% if ticket.assigned? %>
                <span class="ticket-assignee-small" title="<%= ticket.assignee_names %>">
                  <i class="fas fa-users"></i> <%= ticket.assignee_names_short %>
                </span>
              <% else %>
                <span class="ticket-assignee-small ticket-unassigned">
                  <i class="fas fa-user-slash"></i> Unassigned
                </span>
              <% end %>
            </td>
            <td>
              <% if ticket.labels.any? %>
                <div class="ticket-labels">
                  <% ticket.labels.each do |label| %>
                    <span class="terminal-label"><%= label.name %></span>
                  <% end %>
                </div>
              <% else %>
                <span class="no-labels">None</span>
              <% end %>
            </td>
            <td>
              <% if ticket.updates.any? %>
                <%= pluralize(ticket.updates.count, 'update') %>
              <% else %>
                <span class="no-updates">None</span>
              <% end %>
            </td>
            <td>
              <div class="ticket-actions">
                <%= link_to "View", ticket, class: "terminal-btn" %>
                <%= link_to "Edit", edit_ticket_path(ticket), class: "terminal-btn" %>
                <%= link_to "Delete", ticket_path(ticket),
                    data: { turbo_method: :delete, turbo_confirm: "Are you sure you want to delete this ticket?" },
                    class: "terminal-btn terminal-btn-danger" %>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    <% end %>
  <% end %>
<% else %>
  <%= modern_card("No Tickets", "ls") do %>
    <div class="empty-state">
      <div class="empty-icon">📋</div>
      <p>No tickets found. Create your first ticket to get started.</p>
      <%= link_to "Create First Ticket", new_ticket_path, class: "terminal-btn terminal-btn-primary" %>
    </div>
  <% end %>
<% end %>
