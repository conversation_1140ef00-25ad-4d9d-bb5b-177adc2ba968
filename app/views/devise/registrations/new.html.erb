<div style="max-width: 500px; margin: 0 auto; padding: 30px; background-color: #fff; border-radius: 8px; box-shadow: 0 0 20px rgba(0,0,0,0.1);">
  <h2 style="text-align: center; margin-bottom: 30px; color: #333; font-size: 28px;">Sign up</h2>

  <%= form_for(resource, as: resource_name, url: registration_path(resource_name)) do |f| %>
    <%= render "devise/shared/error_messages", resource: resource %>

    <div style="margin-bottom: 20px;">
      <%= f.label :username, style: "display: block; margin-bottom: 8px; font-weight: 500; color: #333;" %>
      <%= f.text_field :username, autofocus: true, style: "width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" %>
    </div>

    <div style="margin-bottom: 20px;">
      <%= f.label :email, style: "display: block; margin-bottom: 8px; font-weight: 500; color: #333;" %>
      <%= f.email_field :email, autocomplete: "email", style: "width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" %>
    </div>

    <div style="margin-bottom: 20px;">
      <%= f.label :picture, "Profile Picture URL (optional)", style: "display: block; margin-bottom: 8px; font-weight: 500; color: #333;" %>
      <%= f.text_field :picture, style: "width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" %>
    </div>

    <div style="margin-bottom: 20px;">
      <%= f.label :password, style: "display: block; margin-bottom: 8px; font-weight: 500; color: #333;" %>
      <% if @minimum_password_length %>
      <span style="display: block; margin-bottom: 8px; font-size: 14px; color: #666;">(<%= @minimum_password_length %> characters minimum)</span>
      <% end %>
      <%= f.password_field :password, autocomplete: "new-password", style: "width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" %>
    </div>

    <div style="margin-bottom: 30px;">
      <%= f.label :password_confirmation, style: "display: block; margin-bottom: 8px; font-weight: 500; color: #333;" %>
      <%= f.password_field :password_confirmation, autocomplete: "new-password", style: "width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" %>
    </div>

    <div style="margin-bottom: 20px;">
      <%= f.submit "Sign up", style: "width: 100%; padding: 12px; background-color: #007bff; color: white; border: none; border-radius: 4px; font-size: 16px; font-weight: 500; cursor: pointer; transition: background-color 0.3s;" %>
    </div>
  <% end %>

  <div style="text-align: center; margin-top: 20px;">
    <%= render "devise/shared/links" %>
  </div>
</div>
