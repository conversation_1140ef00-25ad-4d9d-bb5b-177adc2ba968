<div style="max-width: 500px; margin: 0 auto; padding: 30px; background-color: #fff; border-radius: 8px; box-shadow: 0 0 20px rgba(0,0,0,0.1);">
  <h2 style="text-align: center; margin-bottom: 30px; color: #333; font-size: 28px;">Sign in</h2>

  <%= form_for(resource, as: resource_name, url: session_path(resource_name)) do |f| %>
    <div style="margin-bottom: 20px;">
      <%= f.label :email, style: "display: block; margin-bottom: 8px; font-weight: 500; color: #333;" %>
      <%= f.email_field :email, autofocus: true, autocomplete: "email", style: "width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" %>
    </div>

    <div style="margin-bottom: 20px;">
      <%= f.label :password, style: "display: block; margin-bottom: 8px; font-weight: 500; color: #333;" %>
      <%= f.password_field :password, autocomplete: "current-password", style: "width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" %>
    </div>

    <% if devise_mapping.rememberable? %>
      <div style="margin-bottom: 20px; display: flex; align-items: center;">
        <%= f.check_box :remember_me, style: "margin-right: 8px;" %>
        <%= f.label :remember_me, style: "font-weight: 500; color: #333;" %>
      </div>
    <% end %>

    <div style="margin-bottom: 20px;">
      <%= f.submit "Sign in", style: "width: 100%; padding: 12px; background-color: #007bff; color: white; border: none; border-radius: 4px; font-size: 16px; font-weight: 500; cursor: pointer; transition: background-color 0.3s;" %>
    </div>
  <% end %>

  <div style="text-align: center; margin-top: 20px;">
    <%= render "devise/shared/links" %>
  </div>
</div>
