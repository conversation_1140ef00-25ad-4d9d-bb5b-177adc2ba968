<% if resource.errors.any? %>
  <div id="error_explanation" data-turbo-cache="false" style="margin-bottom: 25px; padding: 15px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; color: #721c24;">
    <h2 style="margin-top: 0; margin-bottom: 10px; font-size: 18px; font-weight: 500;">
      <%= I18n.t("errors.messages.not_saved",
                 count: resource.errors.count,
                 resource: resource.class.model_name.human.downcase)
       %>
    </h2>
    <ul style="margin-bottom: 0; padding-left: 20px;">
      <% resource.errors.full_messages.each do |message| %>
        <li style="margin-bottom: 5px;"><%= message %></li>
      <% end %>
    </ul>
  </div>
<% end %>
