<% content_for :title, "Labels" %>

<%= modern_card("Label Actions", "ls -la") do %>
  <div class="ticket-actions">
    <%= link_to new_label_path, class: "terminal-btn terminal-btn-primary" do %>
      <span class="nav-icon">+</span> New Label
    <% end %>
    <%= link_to tickets_path, class: "terminal-btn" do %>
      <span class="nav-icon">📋</span> View Tickets
    <% end %>
  </div>
<% end %>

<div id="labels">
  <% if @labels.any? %>
    <%= modern_card("All Labels", nil) do %>
      <%= modern_table do %>
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Tickets</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <% @labels.each do |label| %>
            <tr>
              <td><%= label.id %></td>
              <td><span class="terminal-label"><%= label.name %></span></td>
              <td><%= label.tickets.count %></td>
              <td>
                <div class="ticket-actions">
                  <%= link_to "Edit", edit_label_path(label), class: "terminal-btn" %>
                  <%= link_to "Delete", label_path(label),
                      data: { turbo_method: :delete, turbo_confirm: "Are you sure you want to delete this label? This will remove the label from all tickets." },
                      class: "terminal-btn terminal-btn-danger" %>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      <% end %>
    <% end %>
  <% else %>
    <%= modern_card("No Labels", "ls") do %>
      <div class="empty-state">
        <div class="empty-icon">🏷️</div>
        <p>No labels found. Create your first label to help organize tickets.</p>
        <%= link_to "Create First Label", new_label_path, class: "terminal-btn terminal-btn-primary" %>
      </div>
    <% end %>
  <% end %>
</div>
