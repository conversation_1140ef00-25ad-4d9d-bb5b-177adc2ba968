<% content_for :title, "Edit Label" %>

<div class="terminal-command">
  <span class="terminal-prompt">labels$</span> vim label_<%= @label.id %>.txt
</div>

<%= terminal_form_for(@label) do |form| %>
  <% if @label.errors.any? %>
    <div class="terminal-alert terminal-alert-error">
      <div>[ERROR] <%= pluralize(@label.errors.count, "error") %> prohibited this label from being saved:</div>

      <ul>
        <% @label.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="terminal-form-group">
    <%= form.label :name %>
    <%= form.text_field :name %>
  </div>

  <div class="terminal-form-actions">
    <%= terminal_submit_button(form, "Update Label") %>
    <%= terminal_back_button(labels_path, "cd ..") %>
    <%= terminal_delete_button(@label, "rm -f") %>
  </div>
<% end %>
