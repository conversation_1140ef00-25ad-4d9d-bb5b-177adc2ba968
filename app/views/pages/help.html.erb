<% content_for :title, "Help & Commands" %>

<%= modern_card("Terminal Tickets Manual", "man tickets") do %>
  <div class="help-content">
    <section class="help-section">
      <h2>Description</h2>
      <p>Terminal Tickets is a command-line style ticket management system. This manual provides information on available commands and navigation.</p>
    </section>

    <section class="help-section">
      <h2>Commands</h2>
      <div class="command-grid">
        <div class="command-item">
          <div class="command-name">cd /tickets</div>
          <div class="command-desc">Navigate to tickets list</div>
        </div>
        <div class="command-item">
          <div class="command-name">cd /labels</div>
          <div class="command-desc">Navigate to labels list</div>
        </div>
        <div class="command-item">
          <div class="command-name">cd ..</div>
          <div class="command-desc">Go back to previous page</div>
        </div>
        <div class="command-item">
          <div class="command-name">ls</div>
          <div class="command-desc">List items in current directory</div>
        </div>
        <div class="command-item">
          <div class="command-name">ls -la</div>
          <div class="command-desc">List items with details</div>
        </div>
        <div class="command-item">
          <div class="command-name">cat [ticket_id]</div>
          <div class="command-desc">View a ticket's details</div>
        </div>
        <div class="command-item">
          <div class="command-name">vim [ticket_id]</div>
          <div class="command-desc">Edit a ticket</div>
        </div>
        <div class="command-item">
          <div class="command-name">rm [ticket_id]</div>
          <div class="command-desc">Delete a ticket</div>
        </div>
        <div class="command-item">
          <div class="command-name">touch new_ticket</div>
          <div class="command-desc">Create a new ticket</div>
        </div>
        <div class="command-item">
          <div class="command-name">touch new_label</div>
          <div class="command-desc">Create a new label</div>
        </div>
        <div class="command-item">
          <div class="command-name">echo "text" >> file</div>
          <div class="command-desc">Append an update to a ticket</div>
        </div>
        <div class="command-item">
          <div class="command-name">grep --status=VALUE</div>
          <div class="command-desc">Filter tickets by status</div>
        </div>
        <div class="command-item">
          <div class="command-name">grep --priority=VALUE</div>
          <div class="command-desc">Filter tickets by priority</div>
        </div>
        <div class="command-item">
          <div class="command-name">grep --points=VALUE</div>
          <div class="command-desc">Filter tickets by story points</div>
        </div>
        <div class="command-item">
          <div class="command-name">grep --assignee=USERNAME</div>
          <div class="command-desc">Filter tickets by assignee</div>
        </div>
        <div class="command-item">
          <div class="command-name">grep --unassigned</div>
          <div class="command-desc">Show unassigned tickets</div>
        </div>
        <div class="command-item">
          <div class="command-name">grep --help</div>
          <div class="command-desc">Show grep command help</div>
        </div>
      </div>
    </section>

    <section class="help-section">
      <h2>Ticket Statuses</h2>
      <p>Tickets can have the following statuses:</p>
      <div class="status-grid">
        <div class="status-item">
          <span class="ticket-status status-none">none</span>
          <span class="status-desc">Default status for new tickets</span>
        </div>
        <div class="status-item">
          <span class="ticket-status status-future">future</span>
          <span class="status-desc">Planned for future implementation</span>
        </div>
        <div class="status-item">
          <span class="ticket-status status-todo">todo</span>
          <span class="status-desc">Ready to be worked on</span>
        </div>
        <div class="status-item">
          <span class="ticket-status status-in-progress">in progress</span>
          <span class="status-desc">Currently being worked on</span>
        </div>
        <div class="status-item">
          <span class="ticket-status status-testing">testing</span>
          <span class="status-desc">In testing phase</span>
        </div>
        <div class="status-item">
          <span class="ticket-status status-qa">QA</span>
          <span class="status-desc">In quality assurance</span>
        </div>
        <div class="status-item">
          <span class="ticket-status status-completed">completed</span>
          <span class="status-desc">Work is complete</span>
        </div>
      </div>
      <p>You can filter tickets by status using the filter form or the <code>grep --status=VALUE</code> command.</p>
    </section>

    <section class="help-section">
      <h2>Ticket Priorities</h2>
      <p>Tickets can have the following priorities:</p>
      <div class="status-grid">
        <div class="status-item">
          <span class="ticket-priority priority-lowest">lowest</span>
          <span class="status-desc">Lowest priority, can be addressed later</span>
        </div>
        <div class="status-item">
          <span class="ticket-priority priority-low">low</span>
          <span class="status-desc">Low priority, should be addressed when time permits</span>
        </div>
        <div class="status-item">
          <span class="ticket-priority priority-medium">medium</span>
          <span class="status-desc">Medium priority, default for new tickets</span>
        </div>
        <div class="status-item">
          <span class="ticket-priority priority-high">high</span>
          <span class="status-desc">High priority, should be addressed soon</span>
        </div>
        <div class="status-item">
          <span class="ticket-priority priority-highest">highest</span>
          <span class="status-desc">Highest priority, should be addressed immediately</span>
        </div>
        <div class="status-item">
          <span class="ticket-priority priority-blocker">blocker</span>
          <span class="status-desc">Blocking progress, must be addressed immediately</span>
        </div>
      </div>
      <p>You can filter tickets by priority using the filter form or the <code>grep --priority=VALUE</code> command.</p>
    </section>

    <section class="help-section">
      <h2>Story Points</h2>
      <p>Tickets can be assigned story points to indicate their complexity and effort:</p>
      <div class="status-grid">
        <div class="status-item">
          <span class="ticket-points">-</span>
          <span class="status-desc">No points assigned (0)</span>
        </div>
        <div class="status-item">
          <span class="ticket-points">1</span>
          <span class="status-desc">Very simple task</span>
        </div>
        <div class="status-item">
          <span class="ticket-points">2</span>
          <span class="status-desc">Simple task</span>
        </div>
        <div class="status-item">
          <span class="ticket-points">3</span>
          <span class="status-desc">Moderate task</span>
        </div>
        <div class="status-item">
          <span class="ticket-points">5</span>
          <span class="status-desc">Significant task</span>
        </div>
        <div class="status-item">
          <span class="ticket-points">8</span>
          <span class="status-desc">Complex task</span>
        </div>
        <div class="status-item">
          <span class="ticket-points">13</span>
          <span class="status-desc">Very complex task</span>
        </div>
        <div class="status-item">
          <span class="ticket-points">21</span>
          <span class="status-desc">Extremely complex task</span>
        </div>
      </div>
      <p>You can filter tickets by points using the filter form or the <code>grep --points=VALUE</code> command.</p>
    </section>

    <section class="help-section">
      <h2>Navigation</h2>
      <p>Use the terminal-style links to navigate through the system. Commands are displayed as clickable links that perform the associated action.</p>
      <p>You can also type commands directly in the terminal input box below. Try typing 'ls', 'cd /tickets', 'touch new_ticket', etc.</p>
    </section>

    <section class="help-section">
      <h2>Styling</h2>
      <p>The interface is designed to mimic a terminal environment with:</p>
      <ul>
        <li>Dark background and light text</li>
        <li>Monospace fonts</li>
        <li>ASCII art decorations</li>
        <li>Command-line style navigation</li>
      </ul>
    </section>

    <section class="help-section">
      <h2>Version</h2>
      <p>Terminal Tickets v1.0.0</p>
    </section>
  </div>
<% end %>

<%= modern_card("Quick Actions", nil) do %>
  <div class="ticket-actions">
    <%= link_to tickets_path, class: "terminal-btn" do %>
      <span class="nav-icon">📋</span> View Tickets
    <% end %>
    <%= link_to labels_path, class: "terminal-btn" do %>
      <span class="nav-icon">🏷️</span> View Labels
    <% end %>
    <%= link_to new_ticket_path, class: "terminal-btn terminal-btn-primary" do %>
      <span class="nav-icon">+</span> New Ticket
    <% end %>
    <%= link_to new_label_path, class: "terminal-btn" do %>
      <span class="nav-icon">+</span> New Label
    <% end %>
  </div>
<% end %>
