<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Terminal Tickets" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Include our terminal stylesheet %>
    <%= stylesheet_link_tag "terminal", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>

    <%# Include Select2 for user-friendly multi-select %>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <%= javascript_importmap_tags %>
  </head>

  <body>
    <div class="terminal-container">
      <%= render 'shared/ascii_logo' %>

      <div class="modern-header">
        <div class="header-title">
          <%= content_for(:title) || "Terminal Tickets" %>
        </div>
        <div class="header-user">
          <% if user_signed_in? %>
            <%= current_user.display_name %>@tickets:~$
          <% else %>
            guest@tickets:~$
          <% end %>
        </div>
      </div>

      <div class="modern-nav">
        <div class="nav-links">
          <%= link_to tickets_path, class: "nav-item" do %>
            <span class="nav-icon">📋</span> Tickets
          <% end %>
          <%= link_to labels_path, class: "nav-item" do %>
            <span class="nav-icon">🏷️</span> Labels
          <% end %>
          <%= link_to help_path, class: "nav-item" do %>
            <span class="nav-icon">❓</span> Help
          <% end %>
        </div>
        <div class="nav-auth">
          <% if user_signed_in? %>
            <%= button_to destroy_user_session_path, method: :delete, class: "nav-item" do %>
              <span class="nav-icon">🚪</span> Logout
            <% end %>
          <% else %>
            <%= link_to new_user_session_path, class: "nav-item" do %>
              <span class="nav-icon">🔑</span> Login
            <% end %>
            <%= link_to new_user_registration_path, class: "nav-item" do %>
              <span class="nav-icon">📝</span> Register
            <% end %>
          <% end %>
        </div>
      </div>

      <% if notice %>
        <div class="modern-alert success">
          <div class="alert-icon">✓</div>
          <div class="alert-message"><%= notice %></div>
        </div>
      <% end %>

      <% if alert %>
        <div class="modern-alert error">
          <div class="alert-icon">⚠</div>
          <div class="alert-message"><%= alert %></div>
        </div>
      <% end %>

      <div class="terminal-content">
        <%= yield %>
      </div>

      <div class="modern-footer">
        <div class="footer-info">
          <%= Time.now.strftime('%Y-%m-%d %H:%M:%S') %> - Terminal Tickets v1.0.0
        </div>
        <div class="footer-links">
          <%= link_to "Help & Commands", help_path, class: "footer-link" %>
        </div>
      </div>
    </div>

    <div class="vim-command-bar" data-controller="terminal">
      <div data-terminal-target="output" class="vim-output"></div>
      <div class="vim-input-container">
        <span class="vim-prompt"><%= controller_name %>$</span>
        <input type="text" data-terminal-target="input" class="vim-input" placeholder="Type a command..." autocomplete="off" data-action="keydown->terminal#handleCommand">
      </div>
    </div>
  </body>
</html>
