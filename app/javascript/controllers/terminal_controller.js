import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="terminal"
export default class extends Controller {
  static targets = ["input", "output"]

  connect() {
    if (this.hasInputTarget) {
      this.inputTarget.focus();

      // Add event listener for focusing the input when clicking anywhere on the page
      document.addEventListener('click', this.focusInput.bind(this));

      // Add command history functionality
      this.commandHistory = [];
      this.historyIndex = -1;

      // Update the prompt based on the current page
      this.updateVimPrompt();

      // Add event listener for Turbo navigation
      document.addEventListener('turbo:load', this.pageChanged.bind(this));

      // Check for echo_content in URL parameters
      this.checkForEchoContent();
    }
  }

  checkForEchoContent() {
    const urlParams = new URLSearchParams(window.location.search);
    const echoContent = urlParams.get('echo_content');

    if (echoContent) {
      // Find the update form textarea and fill it
      const updateForm = document.querySelector('form.terminal-form');
      const textarea = updateForm?.querySelector('textarea');

      if (textarea) {
        textarea.value = echoContent;

        // Focus on the textarea
        textarea.focus();

        // Remove the parameter from the URL to prevent resubmission on refresh
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      }
    }
  }

  disconnect() {
    // Remove event listeners when controller is disconnected
    document.removeEventListener('click', this.focusInput.bind(this));
    document.removeEventListener('turbo:load', this.pageChanged.bind(this));
  }

  focusInput(event) {
    // Don't focus if clicking on a link, button, or input
    if (event && (
      event.target.tagName === 'A' ||
      event.target.tagName === 'BUTTON' ||
      event.target.tagName === 'INPUT' ||
      event.target.tagName === 'TEXTAREA' ||
      event.target.tagName === 'SELECT'
    )) {
      return;
    }

    this.inputTarget.focus();
  }



  handleCommand(event) {
    if (event.key === 'Enter') {
      event.preventDefault();
      const command = this.inputTarget.value.trim();

      // Add command to history if not empty
      if (command !== '') {
        this.commandHistory.unshift(command);
        if (this.commandHistory.length > 50) {
          this.commandHistory.pop();
        }
      }

      this.historyIndex = -1;
      this.executeCommand(command);
      this.inputTarget.value = '';
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      this.navigateHistory(1);
    } else if (event.key === 'ArrowDown') {
      event.preventDefault();
      this.navigateHistory(-1);
    } else if (event.key === 'Escape') {
      event.preventDefault();
      this.inputTarget.value = '';
      this.historyIndex = -1;
    }
  }

  navigateHistory(direction) {
    if (this.commandHistory.length === 0) return;

    this.historyIndex += direction;

    if (this.historyIndex < -1) {
      this.historyIndex = -1;
    } else if (this.historyIndex >= this.commandHistory.length) {
      this.historyIndex = this.commandHistory.length - 1;
    }

    if (this.historyIndex === -1) {
      this.inputTarget.value = '';
    } else {
      this.inputTarget.value = this.commandHistory[this.historyIndex];
    }
  }

  executeCommand(command) {
    // Add the command to the output
    const commandLine = document.createElement('div');
    commandLine.className = 'terminal-command';
    commandLine.innerHTML = `<span class="terminal-prompt">${this.getPrompt()}</span> ${command}`;
    this.outputTarget.appendChild(commandLine);

    // Process the command
    const response = document.createElement('div');
    response.className = 'terminal-response';

    if (command === 'ls' || command === 'ls -la') {
      this.handleLsCommand(response);
    } else if (command.startsWith('cd ')) {
      this.handleCdCommand(command);
    } else if (command.startsWith('touch ')) {
      this.handleTouchCommand(command);
    } else if (command.startsWith('cat ')) {
      this.handleCatCommand(command, response);
    } else if (command.startsWith('vim ')) {
      this.handleVimCommand(command, response);
    } else if (command.startsWith('rm ')) {
      this.handleRmCommand(command, response);
    } else if (command.startsWith('echo ')) {
      this.handleEchoCommand(command, response);
    } else if (command.startsWith('grep ')) {
      this.handleGrepCommand(command, response);
    } else if (command === 'clear') {
      this.outputTarget.innerHTML = '';
    } else if (command === 'help' || command === 'man tickets') {
      window.location.href = '/help';
    } else {
      response.innerHTML = `Command not found: ${command}`;
      this.outputTarget.appendChild(response);
    }
  }

  handleLsCommand(response) {
    const currentPath = window.location.pathname;

    if (currentPath === '/tickets') {
      response.innerHTML = 'Listing tickets...';
    } else if (currentPath === '/labels') {
      response.innerHTML = 'Listing labels...';
    } else {
      response.innerHTML = `
        <div>README.md</div>
        <div>commands.txt</div>
        <div>manual.txt</div>
      `;
    }

    this.outputTarget.appendChild(response);
  }

  handleCdCommand(command) {
    const path = command.split(' ')[1];

    if (path === '/tickets') {
      window.location.href = '/tickets';
    } else if (path === '/labels') {
      window.location.href = '/labels';
    } else if (path === '..') {
      const currentPath = window.location.pathname;
      const parentPath = currentPath.substring(0, currentPath.lastIndexOf('/'));
      window.location.href = parentPath || '/';
    } else {
      const response = document.createElement('div');
      response.className = 'terminal-response';
      response.innerHTML = `Directory not found: ${path}`;
      this.outputTarget.appendChild(response);
    }
  }

  handleTouchCommand(command) {
    const filename = command.split(' ')[1];

    if (filename === 'new_ticket') {
      window.location.href = '/tickets/new';
    } else if (filename === 'new_label') {
      window.location.href = '/labels/new';
    } else {
      const response = document.createElement('div');
      response.className = 'terminal-response';
      response.innerHTML = `Cannot create file: ${filename}`;
      this.outputTarget.appendChild(response);
    }
  }

  handleCatCommand(command, response) {
    const ticketId = command.split(' ')[1];
    if (ticketId && !isNaN(ticketId)) {
      window.location.href = `/tickets/${ticketId}`;
    } else {
      response.innerHTML = 'Error: Invalid ticket ID';
      this.outputTarget.appendChild(response);
    }
  }

  handleVimCommand(command, response) {
    const ticketId = command.split(' ')[1];
    if (ticketId && !isNaN(ticketId)) {
      window.location.href = `/tickets/${ticketId}/edit`;
    } else {
      response.innerHTML = 'Error: Invalid ticket ID';
      this.outputTarget.appendChild(response);
    }
  }

  handleRmCommand(command, response) {
    const ticketId = command.split(' ')[1];
    if (ticketId && !isNaN(ticketId)) {
      response.innerHTML = 'To delete a ticket, please visit the ticket page first.';
      this.outputTarget.appendChild(response);
    } else {
      response.innerHTML = 'Error: Invalid ticket ID';
      this.outputTarget.appendChild(response);
    }
  }

  handleEchoCommand(command, response) {
    // Parse the echo command: echo "content" >> ticket_id_updates.log
    const echoRegex = /echo\s+["'](.+?)["']\s+>>\s+ticket_(\d+)_updates\.log/;
    const match = command.match(echoRegex);

    if (!match) {
      response.innerHTML = 'Error: Invalid echo command format. Use: echo "Your update content" >> ticket_ID_updates.log';
      this.outputTarget.appendChild(response);
      return;
    }

    const content = match[1];
    const ticketId = match[2];

    // Check if we're on a ticket page and the IDs match
    const currentPath = window.location.pathname;
    const currentTicketId = currentPath.match(/\/tickets\/(\d+)/)?.[1];

    if (currentTicketId && currentTicketId === ticketId) {
      // We're on the correct ticket page, submit the update
      this.submitUpdate(ticketId, content, response);
    } else {
      // Navigate to the ticket page first
      window.location.href = `/tickets/${ticketId}?echo_content=${encodeURIComponent(content)}`;
    }
  }

  submitUpdate(ticketId, content, response) {
    // Check if user is logged in
    if (!document.querySelector('form.terminal-form')) {
      response.innerHTML = 'Error: You must be logged in to add updates.';
      this.outputTarget.appendChild(response);
      return;
    }

    // Get the CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Create and submit the form
    fetch(`/tickets/${ticketId}/updates`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken
      },
      body: JSON.stringify({ update: { content: content } })
    })
    .then(res => {
      if (res.ok) {
        // Reload the page to show the new update
        window.location.reload();
      } else {
        response.innerHTML = 'Error: Failed to add update. Please try again.';
        this.outputTarget.appendChild(response);
      }
    })
    .catch(err => {
      console.error('Error submitting update:', err);
      response.innerHTML = 'Error: Failed to add update. Please try again.';
      this.outputTarget.appendChild(response);
    });
  }

  getPrompt() {
    const currentPath = window.location.pathname;

    if (currentPath.startsWith('/tickets')) {
      return 'tickets$';
    } else if (currentPath.startsWith('/labels')) {
      return 'labels$';
    } else if (currentPath === '/help') {
      return 'help$';
    } else {
      return '$';
    }
  }

  // Update the prompt in the vim command bar
  updateVimPrompt() {
    const vimPrompt = document.querySelector('.vim-prompt');
    if (vimPrompt) {
      vimPrompt.textContent = this.getPrompt();
    }
  }

  // Handle grep command for filtering tickets
  handleGrepCommand(command, response) {
    const currentPath = window.location.pathname;

    // Only work on the tickets page
    if (!currentPath.startsWith('/tickets') || currentPath !== '/tickets') {
      response.innerHTML = 'Error: grep command can only be used on the tickets index page.';
      this.outputTarget.appendChild(response);
      return;
    }

    // Parse the command: grep --status=value
    const statusMatch = command.match(/grep\s+--status(?:=|\s+)([a-zA-Z\s]+)/);

    // Parse the command: grep --priority=value
    const priorityMatch = command.match(/grep\s+--priority(?:=|\s+)([a-zA-Z\s]+)/);

    // Parse the command: grep --points=value
    const pointsMatch = command.match(/grep\s+--points(?:=|\s+)([0-9]+)/);

    // Parse the command: grep --assignee=username
    const assigneeMatch = command.match(/grep\s+--assignee(?:=|\s+)([a-zA-Z0-9_\s]+)/);

    // Parse the command: grep --unassigned
    const unassignedMatch = command.match(/grep\s+--unassigned/);

    if (statusMatch) {
      const status = statusMatch[1].trim();

      // Check if the status is valid
      const validStatuses = ['none', 'future', 'todo', 'in progress', 'testing', 'QA', 'completed'];
      if (validStatuses.includes(status.toLowerCase())) {
        response.innerHTML = `Filtering tickets by status: ${status}...`;
        this.outputTarget.appendChild(response);

        // Redirect to the filtered page
        window.location.href = `/tickets?status=${encodeURIComponent(status)}`;
      } else {
        response.innerHTML = `Error: Invalid status "${status}". Valid statuses are: ${validStatuses.join(', ')}`;
        this.outputTarget.appendChild(response);
      }
    } else if (priorityMatch) {
      const priority = priorityMatch[1].trim();

      // Check if the priority is valid
      const validPriorities = ['lowest', 'low', 'medium', 'high', 'highest', 'blocker'];
      if (validPriorities.includes(priority.toLowerCase())) {
        response.innerHTML = `Filtering tickets by priority: ${priority}...`;
        this.outputTarget.appendChild(response);

        // Redirect to the filtered page
        window.location.href = `/tickets?priority=${encodeURIComponent(priority)}`;
      } else {
        response.innerHTML = `Error: Invalid priority "${priority}". Valid priorities are: ${validPriorities.join(', ')}`;
        this.outputTarget.appendChild(response);
      }
    } else if (pointsMatch) {
      const points = pointsMatch[1].trim();

      // Check if the points value is valid
      const validPoints = [0, 1, 2, 3, 5, 8, 13, 21];
      const pointsNum = parseInt(points, 10);

      if (validPoints.includes(pointsNum)) {
        response.innerHTML = `Filtering tickets by points: ${pointsNum === 0 ? 'None' : pointsNum}...`;
        this.outputTarget.appendChild(response);

        // Redirect to the filtered page
        window.location.href = `/tickets?points=${pointsNum}`;
      } else {
        response.innerHTML = `Error: Invalid points value "${points}". Valid points values are: ${validPoints.join(', ')}`;
        this.outputTarget.appendChild(response);
      }
    } else if (assigneeMatch) {
      const assignee = assigneeMatch[1].trim();

      // Fetch users to validate the assignee
      fetch('/users.json')
        .then(response => response.json())
        .then(users => {
          const user = users.find(u => u.username.toLowerCase() === assignee.toLowerCase() ||
                                      u.display_name.toLowerCase() === assignee.toLowerCase());

          if (user) {
            const responseElement = document.createElement('div');
            responseElement.innerHTML = `Filtering tickets assigned to: ${user.display_name}...`;
            this.outputTarget.appendChild(responseElement);

            // Redirect to the filtered page
            window.location.href = `/tickets?user_id=${user.id}`;
          } else {
            const responseElement = document.createElement('div');
            responseElement.innerHTML = `Error: User "${assignee}" not found.`;
            this.outputTarget.appendChild(responseElement);
          }
        })
        .catch(error => {
          console.error('Error fetching users:', error);
          const responseElement = document.createElement('div');
          responseElement.innerHTML = 'Error: Could not fetch users.';
          this.outputTarget.appendChild(responseElement);
        });
    } else if (unassignedMatch) {
      response.innerHTML = 'Filtering unassigned tickets...';
      this.outputTarget.appendChild(response);

      // Redirect to show unassigned tickets (user_id=0 will be handled by the controller)
      window.location.href = '/tickets?user_id=0';
    } else if (command === 'grep --help') {
      response.innerHTML = `
        <div>Usage:</div>
        <div>grep --status=VALUE - Filter tickets by status</div>
        <div>grep --priority=VALUE - Filter tickets by priority</div>
        <div>grep --points=VALUE - Filter tickets by story points</div>
        <div>grep --assignee=USERNAME - Filter tickets by assignee</div>
        <div>grep --unassigned - Show unassigned tickets</div>
        <div>Valid status values: none, future, todo, in progress, testing, QA, completed</div>
        <div>Valid priority values: lowest, low, medium, high, highest, blocker</div>
        <div>Valid points values: 0, 1, 2, 3, 5, 8, 13, 21</div>
      `;
      this.outputTarget.appendChild(response);
    } else {
      response.innerHTML = 'Error: Invalid grep command. Try "grep --help" for usage information.';
      this.outputTarget.appendChild(response);
    }
  }

  // Called when the page changes
  pageChanged() {
    this.updateVimPrompt();
    this.checkForEchoContent();
  }
}
