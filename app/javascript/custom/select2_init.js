document.addEventListener('turbo:load', function() {
  // Initialize Select2 for multi-select user fields
  $('.user-select2').select2({
    theme: 'classic',
    placeholder: 'Select assignees...',
    allowClear: true,
    width: '100%',
    closeOnSelect: false,
    templateResult: formatUser,
    templateSelection: formatUserSelection
  });

  // Format the dropdown items
  function formatUser(user) {
    if (!user.id) {
      return user.text;
    }
    
    return $('<span><i class="fas fa-user"></i> ' + user.text + '</span>');
  }

  // Format the selected items
  function formatUserSelection(user) {
    if (!user.id) {
      return user.text;
    }
    
    return $('<span><i class="fas fa-user"></i> ' + user.text + '</span>');
  }
});

// Also initialize on Turbo navigation
document.addEventListener('turbo:render', function() {
  // Initialize Select2 for multi-select user fields
  $('.user-select2').select2({
    theme: 'classic',
    placeholder: 'Select assignees...',
    allowClear: true,
    width: '100%',
    closeOnSelect: false,
    templateResult: formatUser,
    templateSelection: formatUserSelection
  });

  // Format the dropdown items
  function formatUser(user) {
    if (!user.id) {
      return user.text;
    }
    
    return $('<span><i class="fas fa-user"></i> ' + user.text + '</span>');
  }

  // Format the selected items
  function formatUserSelection(user) {
    if (!user.id) {
      return user.text;
    }
    
    return $('<span><i class="fas fa-user"></i> ' + user.text + '</span>');
  }
});
