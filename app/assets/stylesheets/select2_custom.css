/* Custom styling for Select2 to match terminal theme */

.select2-container--classic .select2-selection--multiple {
  background-color: #1a1b26 !important;
  border: 1px solid #414868 !important;
  border-radius: 4px !important;
  color: #c0caf5 !important;
  min-height: 36px !important;
}

.select2-container--classic .select2-selection--multiple:focus {
  border-color: #7aa2f7 !important;
  outline: none !important;
}

.select2-container--classic .select2-selection--multiple .select2-selection__choice {
  background-color: #414868 !important;
  border: 1px solid #565f89 !important;
  border-radius: 4px !important;
  color: #c0caf5 !important;
  margin-top: 5px !important;
  margin-left: 5px !important;
  padding: 2px 6px !important;
}

.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove {
  color: #f7768e !important;
  margin-right: 5px !important;
}

.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #ff9e64 !important;
}

.select2-container--classic .select2-dropdown {
  background-color: #1a1b26 !important;
  border: 1px solid #414868 !important;
}

.select2-container--classic .select2-results__option {
  color: #c0caf5 !important;
  padding: 6px 12px !important;
}

.select2-container--classic .select2-results__option--highlighted[aria-selected] {
  background-color: #414868 !important;
  color: #c0caf5 !important;
}

.select2-container--classic .select2-search--dropdown .select2-search__field {
  background-color: #1a1b26 !important;
  border: 1px solid #414868 !important;
  color: #c0caf5 !important;
  padding: 6px 8px !important;
}

.select2-container--classic .select2-search--inline .select2-search__field {
  background-color: transparent !important;
  color: #c0caf5 !important;
  border: none !important;
}

.select2-container--classic .select2-selection--multiple .select2-selection__rendered {
  padding: 0 5px !important;
}

.select2-container--classic .select2-selection--multiple .select2-selection__placeholder {
  color: #565f89 !important;
}

/* Adjust the size of the dropdown to be more compact but still usable */
.select2-container--classic .select2-results__option {
  font-size: 14px !important;
  line-height: 1.4 !important;
}

/* Make the dropdown scrollable for many users */
.select2-results__options {
  max-height: 300px !important;
  overflow-y: auto !important;
}
