/* Terminal Theme Styling */
:root {
  --terminal-bg: #1a1b26;
  --terminal-text: #c0caf5;
  --terminal-border: #414868;
  --terminal-highlight: #bb9af7;
  --terminal-accent: #f7768e;
  --terminal-success: #9ece6a;
  --terminal-warning: #e0af68;
  --terminal-error: #f7768e;
  --terminal-link: #7aa2f7;
  --terminal-header: #414868;
  --terminal-font: 'Courier New', monospace;

  /* Modern theme additions */
  --card-bg: rgba(65, 72, 104, 0.2);
  --card-border: #414868;
  --card-shadow: rgba(0, 0, 0, 0.2);
  --card-header-bg: rgba(65, 72, 104, 0.4);
  --card-radius: 8px;
}

body {
  background-color: var(--terminal-bg);
  color: var(--terminal-text);
  font-family: var(--terminal-font);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  font-size: 16px;
}

/* ASCII Art Header */
.ascii-header {
  font-family: monospace;
  white-space: pre;
  line-height: 1.2;
  color: var(--terminal-highlight);
  text-align: center;
  margin: 20px 0;
  font-size: 14px;
}

/* Terminal Container */
.terminal-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid var(--terminal-border);
  border-radius: 5px;
  background-color: var(--terminal-bg);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

/* Modern Header */
.modern-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--terminal-border);
}

.header-title {
  font-size: 24px;
  font-weight: bold;
  color: var(--terminal-highlight);
}

.header-user {
  color: var(--terminal-accent);
  font-size: 16px;
}

/* Modern Navigation */
.modern-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding: 10px 15px;
  background-color: var(--card-bg);
  border-radius: var(--card-radius);
  box-shadow: 0 2px 8px var(--card-shadow);
}

.nav-links, .nav-auth {
  display: flex;
  gap: 10px;
  align-items: center;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  color: var(--terminal-text);
  text-decoration: none;
  border-radius: 6px;
  font-size: 15px;
  transition: all 0.2s ease;
  background: none;
  border: none;
  cursor: pointer;
}

.nav-item:hover {
  color: var(--terminal-highlight);
  background-color: rgba(65, 72, 104, 0.5);
}

.nav-icon {
  font-size: 16px;
}

/* Keep the old terminal nav styles for backward compatibility */
.terminal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px dashed var(--terminal-border);
  margin-bottom: 10px;
}

.terminal-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--terminal-highlight);
}

.terminal-user {
  color: var(--terminal-accent);
}

.terminal-nav {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
  padding: 5px 10px;
  background-color: var(--terminal-bg);
  border: 1px solid var(--terminal-border);
  border-radius: 3px;
}

.terminal-nav-item {
  display: inline-block;
  padding: 3px 8px;
  color: var(--terminal-text);
  text-decoration: none;
  border: 1px solid var(--terminal-border);
  border-radius: 3px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.terminal-nav-item:before {
  content: "> ";
  color: var(--terminal-highlight);
}

.terminal-nav-item:hover {
  color: var(--terminal-highlight);
  background-color: rgba(65, 72, 104, 0.3);
  border-color: var(--terminal-highlight);
}

.nav-btn-wrapper {
  display: inline-block;
}

.nav-btn-wrapper form {
  margin: 0;
  padding: 0;
}

.nav-btn-wrapper .terminal-nav-item {
  border: 1px solid var(--terminal-border);
  background: none;
  cursor: pointer;
}

/* Terminal Content */
.terminal-content {
  padding: 10px 0;
}

/* Modern Table */
.modern-table-container {
  width: 100%;
  overflow-x: auto;
  margin: 20px 0;
  border-radius: var(--card-radius);
  box-shadow: 0 2px 8px var(--card-shadow);
  background-color: var(--card-bg);
}

.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.modern-table th {
  text-align: left;
  padding: 12px 15px;
  background-color: var(--card-header-bg);
  color: var(--terminal-highlight);
  font-weight: normal;
  border-bottom: 1px solid var(--terminal-border);
}

.modern-table th:first-child {
  border-top-left-radius: var(--card-radius);
}

.modern-table th:last-child {
  border-top-right-radius: var(--card-radius);
}

.modern-table td {
  padding: 12px 15px;
  border-bottom: 1px solid rgba(65, 72, 104, 0.3);
}

.modern-table tr:last-child td {
  border-bottom: none;
}

.modern-table tr:hover td {
  background-color: rgba(65, 72, 104, 0.3);
}

/* Keep old table styles for backward compatibility */
.terminal-table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
}

.terminal-table th {
  text-align: left;
  padding: 8px;
  border-bottom: 1px solid var(--terminal-border);
  color: var(--terminal-highlight);
}

.terminal-table td {
  padding: 8px;
  border-bottom: 1px dashed var(--terminal-border);
}

/* Terminal Forms */
.terminal-form {
  margin: 20px 0;
}

/* Terminal Input */
.terminal-input {
  background-color: var(--terminal-bg);
  border: none;
  border-bottom: 1px dashed var(--terminal-border);
  color: var(--terminal-text);
  font-family: var(--terminal-font);
  font-size: 16px;
  padding: 5px;
  width: 80%;
  outline: none;
}

.terminal-input:focus {
  border-bottom: 1px solid var(--terminal-highlight);
}

/* Terminal Output */
.terminal-output {
  margin: 10px 0;
  max-height: 300px;
  overflow-y: auto;
}

.terminal-response {
  color: var(--terminal-text);
  font-family: var(--terminal-font);
  margin-left: 20px;
  padding: 5px 0;
}

/* Vim Command Bar */
.vim-command-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: var(--terminal-bg);
  border-top: 1px solid var(--terminal-border);
  padding: 5px 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.vim-input-container {
  display: flex;
  align-items: center;
}

.vim-prompt {
  color: var(--terminal-highlight);
  margin-right: 10px;
  font-family: var(--terminal-font);
  font-weight: bold;
}

.vim-input {
  background-color: var(--terminal-bg);
  border: none;
  color: var(--terminal-text);
  font-family: var(--terminal-font);
  font-size: 16px;
  padding: 5px;
  flex-grow: 1;
  outline: none;
}

.vim-output {
  max-height: 150px;
  overflow-y: auto;
  margin-bottom: 5px;
  font-family: var(--terminal-font);
  color: var(--terminal-text);
  font-size: 14px;
}

/* Add padding to body to prevent content from being hidden behind the vim command bar */
body {
  padding-bottom: 40px;
}

.terminal-form-group {
  margin-bottom: 15px;
}

.terminal-form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.terminal-form-row .terminal-form-group {
  flex: 1;
  min-width: 0;
}

.terminal-form-group label {
  display: block;
  margin-bottom: 5px;
  color: var(--terminal-highlight);
}

.terminal-form-group input[type="text"],
.terminal-form-group input[type="email"],
.terminal-form-group input[type="password"],
.terminal-form-group textarea,
.terminal-form-group select.terminal-select {
  width: 100%;
  padding: 8px;
  background-color: var(--terminal-bg);
  border: 1px solid var(--terminal-border);
  color: var(--terminal-text);
  font-family: var(--terminal-font);
}

.terminal-form-group input[type="text"]:focus,
.terminal-form-group input[type="email"]:focus,
.terminal-form-group input[type="password"]:focus,
.terminal-form-group textarea:focus,
.terminal-form-group select.terminal-select:focus {
  outline: none;
  border-color: var(--terminal-highlight);
}

.terminal-form-group select.terminal-select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23c0caf5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  padding-right: 30px;
}

/* Terminal Buttons */
.terminal-btn {
  display: inline-block;
  padding: 3px 8px;
  background-color: var(--terminal-bg);
  border: 1px solid var(--terminal-border);
  color: var(--terminal-text);
  text-decoration: none;
  cursor: pointer;
  font-family: var(--terminal-font);
  font-size: 14px;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.terminal-btn:hover {
  background-color: rgba(65, 72, 104, 0.3);
  color: var(--terminal-highlight);
  border-color: var(--terminal-highlight);
}

.terminal-btn-primary {
  border-color: var(--terminal-highlight);
  color: var(--terminal-highlight);
}

.terminal-btn-danger {
  border-color: var(--terminal-error);
  color: var(--terminal-error);
}

.terminal-btn-success {
  border-color: var(--terminal-success);
  color: var(--terminal-success);
}

/* Modern Alerts */
.modern-alert {
  display: flex;
  align-items: center;
  padding: 15px;
  margin: 20px 0;
  border-radius: var(--card-radius);
  box-shadow: 0 2px 8px var(--card-shadow);
}

.modern-alert.success {
  background-color: rgba(158, 206, 106, 0.1);
  border-left: 4px solid var(--terminal-success);
}

.modern-alert.error {
  background-color: rgba(247, 118, 142, 0.1);
  border-left: 4px solid var(--terminal-error);
}

.modern-alert.warning {
  background-color: rgba(224, 175, 104, 0.1);
  border-left: 4px solid var(--terminal-warning);
}

.alert-icon {
  font-size: 20px;
  margin-right: 15px;
}

.success .alert-icon {
  color: var(--terminal-success);
}

.error .alert-icon {
  color: var(--terminal-error);
}

.warning .alert-icon {
  color: var(--terminal-warning);
}

.alert-message {
  flex: 1;
}

/* Modern Footer */
.modern-footer {
  margin-top: 40px;
  padding: 20px 0;
  border-top: 1px solid var(--terminal-border);
  text-align: center;
  font-size: 14px;
  color: var(--terminal-border);
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.footer-link {
  color: var(--terminal-link);
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: var(--terminal-highlight);
  text-decoration: underline;
}

/* Keep old alert styles for backward compatibility */
.terminal-alert {
  padding: 10px;
  margin: 15px 0;
  border: 1px dashed var(--terminal-border);
}

.terminal-alert-success {
  border-color: var(--terminal-success);
  color: var(--terminal-success);
}

.terminal-alert-error {
  border-color: var(--terminal-error);
  color: var(--terminal-error);
}

.terminal-alert-warning {
  border-color: var(--terminal-warning);
  color: var(--terminal-warning);
}

/* Keep old footer styles for backward compatibility */
.terminal-footer {
  margin-top: 30px;
  padding-top: 15px;
  border-top: 1px dashed var(--terminal-border);
  text-align: center;
  font-size: 14px;
  color: var(--terminal-border);
}

/* Terminal Labels */
.terminal-label {
  display: inline-block;
  padding: 2px 6px;
  margin: 2px;
  border: 1px solid var(--terminal-border);
  border-radius: 3px;
}

/* Ticket Status */
.ticket-status {
  display: inline-block;
  padding: 3px 8px;
  margin: 2px;
  border-radius: 4px;
  font-size: 0.9em;
  color: var(--terminal-bg);
  font-weight: bold;
  text-transform: uppercase;
}

/* Ticket Meta */
.ticket-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Ticket Assignee */
.ticket-assignee {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 3px 8px;
  border-radius: 4px;
  background-color: var(--terminal-highlight);
  color: var(--terminal-bg);
  font-size: 0.9em;
  font-weight: bold;
}

.ticket-assignee-small {
  display: inline-flex;
  align-items: center;
  gap: 3px;
  padding: 2px 6px;
  border-radius: 3px;
  background-color: var(--terminal-highlight);
  color: var(--terminal-bg);
  font-size: 0.8em;
  white-space: nowrap;
}

.ticket-unassigned {
  background-color: var(--terminal-border);
  color: var(--terminal-text);
  opacity: 0.8;
}

.status-none {
  background-color: #414868;
  color: var(--terminal-text);
}

.status-future {
  background-color: #7aa2f7;
}

.status-todo {
  background-color: #e0af68;
}

.status-in-progress {
  background-color: #bb9af7;
}

.status-testing {
  background-color: #7dcfff;
}

.status-qa {
  background-color: #9ece6a;
}

.status-completed {
  background-color: #1a1b26;
  color: var(--terminal-text);
  border: 1px solid var(--terminal-border);
}

/* Ticket Priority */
.ticket-priority {
  display: inline-block;
  padding: 3px 8px;
  margin: 2px;
  border-radius: 4px;
  font-size: 0.9em;
  color: var(--terminal-bg);
  font-weight: bold;
  text-transform: uppercase;
}

.priority-lowest {
  background-color: #414868;
  color: var(--terminal-text);
}

.priority-low {
  background-color: #7aa2f7;
}

.priority-medium {
  background-color: #e0af68;
}

.priority-high {
  background-color: #ff9e64;
}

.priority-highest {
  background-color: #f7768e;
}

.priority-blocker {
  background-color: #db4b4b;
}

/* Ticket Points */
.ticket-points {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--terminal-border);
  color: var(--terminal-text);
  font-size: 0.8em;
  font-weight: bold;
}

/* Terminal Links */
a {
  color: var(--terminal-link);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Terminal Command Line */
.terminal-command {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.terminal-prompt {
  color: var(--terminal-highlight);
  margin-right: 10px;
}

/* ASCII Box Drawing */
.ascii-box {
  border: 1px solid var(--terminal-border);
  padding: 10px;
  margin: 10px 0;
  position: relative;
}

.ascii-box:before {
  content: "+";
  position: absolute;
  top: -1px;
  left: -1px;
  color: var(--terminal-highlight);
}

.ascii-box:after {
  content: "+";
  position: absolute;
  top: -1px;
  right: -1px;
  color: var(--terminal-highlight);
}

.ascii-box-footer:before {
  content: "+";
  position: absolute;
  bottom: -1px;
  left: -1px;
  color: var(--terminal-highlight);
}

.ascii-box-footer:after {
  content: "+";
  position: absolute;
  bottom: -1px;
  right: -1px;
  color: var(--terminal-highlight);
}

/* Modern Ticket Card Styling */
.modern-ticket-card {
  background-color: var(--terminal-bg);
  border-radius: 8px;
  border: 1px solid var(--terminal-border);
  margin: 20px 0;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.modern-ticket-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.ticket-header {
  background-color: rgba(65, 72, 104, 0.3);
  padding: 12px 16px;
  border-bottom: 1px solid var(--terminal-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ticket-id {
  font-weight: bold;
  color: var(--terminal-highlight);
  font-size: 18px;
}

.ticket-content {
  padding: 16px;
}

.ticket-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px dashed var(--terminal-border);
}

.ticket-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.ticket-title {
  color: var(--terminal-text);
  font-size: 22px;
  margin: 0 0 8px 0;
  font-weight: normal;
}

.section-title {
  color: var(--terminal-highlight);
  font-size: 16px;
  margin: 0 0 8px 0;
  font-weight: normal;
}

.ticket-description {
  color: var(--terminal-text);
  line-height: 1.6;
}

.ticket-labels {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.no-labels, .no-updates {
  color: var(--terminal-border);
  font-style: italic;
}

.latest-update {
  margin-top: 4px;
  color: var(--terminal-text);
  font-size: 14px;
}

/* Ticket Actions */
.ticket-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* Update Items */
.terminal-updates {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.update-item {
  padding: 12px;
  border: 1px solid var(--terminal-border);
  border-radius: 6px;
  background-color: rgba(65, 72, 104, 0.1);
}

.update-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px dashed var(--terminal-border);
}

.update-user {
  color: var(--terminal-highlight);
  font-weight: bold;
}

.update-time {
  color: var(--terminal-border);
  font-size: 14px;
}

.update-content {
  color: var(--terminal-text);
  line-height: 1.5;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: var(--terminal-border);
}

.empty-state p {
  margin-bottom: 20px;
  color: var(--terminal-text);
  font-size: 16px;
}

/* Help Page Styling */
.help-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.help-section {
  margin-bottom: 10px;
}

.help-section h2 {
  color: var(--terminal-highlight);
  font-size: 20px;
  margin-bottom: 10px;
  font-weight: normal;
  border-bottom: 1px dashed var(--terminal-border);
  padding-bottom: 5px;
}

.help-section p {
  margin-bottom: 10px;
  line-height: 1.6;
}

.help-section ul {
  list-style-type: none;
  padding-left: 20px;
}

.help-section li {
  position: relative;
  padding-left: 15px;
  margin-bottom: 5px;
  line-height: 1.6;
}

.help-section li:before {
  content: ">";
  position: absolute;
  left: 0;
  color: var(--terminal-highlight);
}

.command-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.command-item {
  padding: 10px;
  border: 1px solid var(--terminal-border);
  border-radius: 6px;
  background-color: rgba(65, 72, 104, 0.2);
}

.command-name {
  color: var(--terminal-highlight);
  font-weight: bold;
  margin-bottom: 5px;
  font-family: monospace;
}

.command-desc {
  color: var(--terminal-text);
  font-size: 14px;
}

/* Status Grid in Help Page */
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 10px;
  margin: 15px 0;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 4px;
  background-color: rgba(65, 72, 104, 0.2);
}

.status-desc {
  color: var(--terminal-text);
  font-size: 14px;
}

/* Filter Form Styles */
.filter-form {
  margin: 0;
}

.filter-form-row {
  display: flex;
  align-items: flex-end;
  gap: 15px;
  flex-wrap: wrap;
}

.filter-form-row .terminal-form-group {
  flex: 1;
  min-width: 200px;
  margin-bottom: 10px;
}

.filter-form-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.active-filters {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed var(--terminal-border);
}

.active-filter-label {
  color: var(--terminal-highlight);
  font-weight: bold;
}

.active-filter-tag {
  display: flex;
  gap: 5px;
}
