/*
 * This is a manifest file that'll be compiled into application.css.
 *
 * With Propshaft, assets are served efficiently without preprocessing steps. You can still include
 * application-wide styles in this file, but keep in mind that CSS precedence will follow the standard
 * cascading order, meaning styles declared later in the document or manifest will override earlier ones,
 * depending on specificity.
 *
 * Consider organizing styles into separate files for maintainability.
 */

/*= require select2_custom */

/* Additional styling for Select2 container */
.select2-container-wrapper {
  margin-bottom: 10px;
}

/* Make sure the Select2 dropdown is visible and properly positioned */
.select2-container--open .select2-dropdown {
  z-index: 9999;
}

/* Ensure the Select2 container has proper width */
.select2-container {
  width: 100% !important;
}

/* Style for the multi-select container to match terminal theme */
.user-select2, .user-select2-single {
  background-color: #1a1b26 !important;
  color: #c0caf5 !important;
  border: 1px solid #414868 !important;
}

/* Ensure the dropdown is visible on top of other elements */
.select2-container--classic .select2-dropdown {
  z-index: 9999 !important;
}

/* Improve the display of assignees */
.ticket-assignee, .ticket-assignee-small {
  display: inline-block;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
}

/* Add a hover effect to show the full list on hover */
.ticket-assignee:hover, .ticket-assignee-small:hover {
  position: relative;
  overflow: visible;
  z-index: 1000;
}

.ticket-assignee:hover::after, .ticket-assignee-small:hover::after {
  content: attr(title);
  position: absolute;
  left: 0;
  top: 100%;
  background-color: #1a1b26;
  border: 1px solid #414868;
  padding: 5px 10px;
  border-radius: 4px;
  white-space: normal;
  max-width: 250px;
  z-index: 1001;
}

/* Styling for the assignee list in ticket view */
.assignee-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 5px;
}

.assignee-item {
  background-color: #414868;
  border-radius: 4px;
  padding: 5px 10px;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
}

.assignee-item i {
  color: #7aa2f7;
}

.no-assignees {
  color: #565f89;
  font-style: italic;
}

.ticket-assignee-indicator {
  color: #7aa2f7;
  margin-left: 5px;
}
