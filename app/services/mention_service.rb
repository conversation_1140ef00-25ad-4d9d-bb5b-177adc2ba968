class MentionService
  # Regular expression to match @username mentions
  # Matches @username where username can contain letters, numbers, underscores, and hyphens
  MENTION_REGEX = /@([a-zA-Z0-9_\-]+)/

  # Extract mentions from text and return the corresponding users
  def self.extract_mentions(text)
    return [] if text.blank?

    # Extract all usernames from the text
    usernames = text.scan(MENTION_REGEX).flatten.uniq

    # Find users with matching usernames
    User.where(username: usernames)
  end

  # Send notification emails to mentioned users
  def self.notify_mentioned_users(update)
    mentioned_users = extract_mentions(update.content)
    
    # Don't notify the author of the update
    mentioned_users = mentioned_users.reject { |user| user.id == update.user_id }
    
    mentioned_users.each do |user|
      NotificationMailer.mention_notification(user, update).deliver_later
    end
    
    mentioned_users
  end
end
