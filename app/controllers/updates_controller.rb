class UpdatesController < ApplicationController
  before_action :authenticate_user!
  before_action :set_ticket

  def create
    @update = @ticket.updates.new(update_params)
    @update.user = current_user

    respond_to do |format|
      if @update.save
        # Process mentions and send notifications
        mentioned_users = MentionService.notify_mentioned_users(@update)

        # Add a notice about mentions if any users were mentioned
        notice = "Update was successfully added."
        if mentioned_users.any?
          notice += " Notifications sent to #{mentioned_users.map(&:display_name).to_sentence}."
        end

        format.html { redirect_to @ticket, notice: notice }
        format.json { render :show, status: :created, location: @ticket }
      else
        format.html { redirect_to @ticket, alert: "Failed to add update: #{@update.errors.full_messages.join(', ')}" }
        format.json { render json: @update.errors, status: :unprocessable_entity }
      end
    end
  end

  private

  def set_ticket
    @ticket = Ticket.find(params[:ticket_id])
  end

  def update_params
    params.require(:update).permit(:content)
  end
end
