class TicketsController < ApplicationController
  before_action :authenticate_user!, except: [:index, :show]
  before_action :set_ticket, only: %i[ show edit update destroy ]

  # GET /tickets or /tickets.json
  def index
    @tickets = Ticket.all

    # Filter by status if status param is present
    if params[:status].present? && Ticket::STATUSES.values.include?(params[:status])
      @tickets = @tickets.where(status: params[:status])
      @active_status = params[:status]
    end

    # Filter by priority if priority param is present
    if params[:priority].present? && Ticket::PRIORITIES.values.include?(params[:priority])
      @tickets = @tickets.where(priority: params[:priority])
      @active_priority = params[:priority]
    end

    # Filter by points if points param is present
    if params[:points].present? && params[:points].to_i >= 0
      @tickets = @tickets.where(points: params[:points])
      @active_points = params[:points].to_i
    end

    # Filter by assignee if user_id param is present
    if params[:user_id].present?
      user_id = params[:user_id].to_i
      if user_id > 0 # Only filter if a valid user ID is provided
        @tickets = @tickets.joins(:users).where(users: { id: user_id })
        @active_user = User.find_by(id: user_id)
      end
    end
  end

  # GET /tickets/1 or /tickets/1.json
  def show
    @update = @ticket.updates.new
    @updates = @ticket.updates.where.not(id: nil)  # Only fetch persisted updates
  end

  # GET /tickets/new
  def new
    @ticket = Ticket.new
  end

  # GET /tickets/1/edit
  def edit
  end

  # POST /tickets or /tickets.json
  def create
    @ticket = Ticket.new(ticket_params.except(:user_ids))

    # Handle user assignments
    if params[:ticket][:user_ids].present?
      @ticket.user_ids = params[:ticket][:user_ids].reject(&:blank?)
    end

    respond_to do |format|
      if @ticket.save
        format.html { redirect_to @ticket, notice: "Ticket was successfully created." }
        format.json { render :show, status: :created, location: @ticket }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @ticket.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /tickets/1 or /tickets/1.json
  def update
    # Update basic ticket attributes
    @ticket.assign_attributes(ticket_params.except(:user_ids))

    # Handle user assignments
    if params[:ticket][:user_ids].present?
      @ticket.user_ids = params[:ticket][:user_ids].reject(&:blank?)
    else
      @ticket.user_ids = []
    end

    # Debug: Log the parameters
    Rails.logger.debug "Ticket params: #{ticket_params.inspect}"
    Rails.logger.debug "User IDs: #{@ticket.user_ids.inspect}"

    respond_to do |format|
      if @ticket.save
        format.html { redirect_to @ticket, notice: "Ticket was successfully updated." }
        format.json { render :show, status: :ok, location: @ticket }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @ticket.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /tickets/1 or /tickets/1.json
  def destroy
    @ticket.destroy!

    respond_to do |format|
      format.html { redirect_to tickets_path, status: :see_other, notice: "Ticket was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_ticket
      @ticket = Ticket.find(params.require(:id))
    end

    # Only allow a list of trusted parameters through.
    def ticket_params
      params.require(:ticket).permit(:title, :description, :status, :priority, :points, user_ids: [], label_ids: [])
    end
end
