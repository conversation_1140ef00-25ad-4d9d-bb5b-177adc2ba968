class NotificationMailer < ApplicationMailer
  default from: 'Cherry Sundae Tickets <<EMAIL>>'

  # Send an email to a user when they are mentioned in a ticket update
  def mention_notification(user, update)
    @user = user
    @update = update
    @ticket = update.ticket
    @mentioner = update.user

    mail(
      to: @user.email,
      subject: "You were mentioned in Ticket ##{@ticket.id}: #{@ticket.title}"
    )
  end
end
