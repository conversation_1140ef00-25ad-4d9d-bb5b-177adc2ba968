class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable

  has_and_belongs_to_many :tickets
  has_many :updates, dependent: :nullify

  validates :username, presence: true, uniqueness: true

  def display_name
    username.presence || email.split('@').first
  end
end
