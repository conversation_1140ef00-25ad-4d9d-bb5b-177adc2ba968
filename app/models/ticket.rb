class Ticket < ApplicationRecord
  has_and_belongs_to_many :users
  has_and_belongs_to_many :labels
  has_many :updates, dependent: :destroy

  # Define available statuses
  STATUSES = {
    none: 'none',
    future: 'future',
    todo: 'todo',
    in_progress: 'in progress',
    testing: 'testing',
    qa: 'QA',
    completed: 'completed'
  }.freeze

  # Define status colors
  STATUS_COLORS = {
    'none' => '#414868',        # Default border color
    'future' => '#7aa2f7',      # terminal-link color
    'todo' => '#e0af68',        # terminal-warning color
    'in progress' => '#bb9af7', # terminal-highlight color
    'testing' => '#7dcfff',     # Light blue
    'QA' => '#9ece6a',          # terminal-success color
    'completed' => '#1a1b26'    # terminal-bg color (dark)
  }.freeze

  # Define available priorities
  PRIORITIES = {
    lowest: 'lowest',
    low: 'low',
    medium: 'medium',
    high: 'high',
    highest: 'highest',
    blocker: 'blocker'
  }.freeze

  # Define priority colors
  PRIORITY_COLORS = {
    'lowest' => '#414868',      # Default border color
    'low' => '#7aa2f7',         # terminal-link color (blue)
    'medium' => '#e0af68',      # terminal-warning color (orange)
    'high' => '#ff9e64',        # Bright orange
    'highest' => '#f7768e',     # terminal-error color (red)
    'blocker' => '#db4b4b'      # Dark red
  }.freeze

  # Define valid point values (Fibonacci sequence commonly used in agile)
  VALID_POINTS = [0, 1, 2, 3, 5, 8, 13, 21].freeze

  validates :title, presence: true
  validates :status, presence: true, inclusion: { in: STATUSES.values }
  validates :priority, presence: true, inclusion: { in: PRIORITIES.values }
  validates :points, numericality: { only_integer: true, greater_than_or_equal_to: 0 }

  # Set default values
  after_initialize :set_default_values, if: :new_record?

  # Method to add an update to the ticket
  def add_update(content, user)
    updates.create(content: content, user: user)
  end

  # Get the color for the current status
  def status_color
    STATUS_COLORS[status] || STATUS_COLORS['none']
  end

  # Get the color for the current priority
  def priority_color
    PRIORITY_COLORS[priority] || PRIORITY_COLORS['medium']
  end

  # Helper method to check if ticket is assigned to any users
  def assigned?
    users.any?
  end

  # Helper method to get a comma-separated list of assignee names
  def assignee_names
    users.map(&:display_name).join(', ')
  end

  # Helper method to get a truncated list of assignee names for display in tables
  def assignee_names_short
    names = users.map(&:display_name)
    if names.length <= 2
      names.join(', ')
    else
      "#{names.first}, #{names.second} +#{names.length - 2}"
    end
  end

  # Format points for display
  def points_display
    points.zero? ? "-" : points.to_s
  end

  private

  def set_default_values
    self.status ||= STATUSES[:none]
    self.priority ||= PRIORITIES[:medium]
    self.points ||= 0
  end
end
