module MentionHelper
  # Process text content to highlight user mentions with special styling
  def process_mentions(text)
    return '' if text.blank?

    # Use the same regex as MentionService to find mentions
    mention_regex = /@([a-zA-Z0-9_\-]+)/

    # First escape HTML to prevent XSS
    escaped_text = ERB::Util.html_escape(text)

    # Replace mentions with styled spans
    processed_text = escaped_text.gsub(mention_regex) do |match|
      username = $1
      user = User.find_by(username: username)

      if user
        # Create a styled mention link
        content_tag(:span, match, class: 'user-mention', title: "#{user.display_name} (#{user.email})")
      else
        # If user doesn't exist, just return the original text
        match
      end
    end

    # Convert line breaks to HTML and return as HTML safe
    simple_format(processed_text.html_safe)
  end

  # Extract all mentioned users from text for display purposes
  def extract_mentioned_users(text)
    return [] if text.blank?

    mention_regex = /@([a-zA-Z0-9_\-]+)/
    usernames = text.scan(mention_regex).flatten.uniq
    User.where(username: usernames)
  end

  # Check if current user is mentioned in the text
  def current_user_mentioned?(text, current_user)
    return false unless current_user && text.present?

    mentioned_users = extract_mentioned_users(text)
    mentioned_users.include?(current_user)
  end
end
