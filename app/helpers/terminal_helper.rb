module TerminalHelper
  def terminal_form_for(record, options = {}, &block)
    options[:html] ||= {}
    options[:html][:class] = [options[:html][:class], "terminal-form"].compact.join(" ")

    content_tag(:div, class: "modern-ticket-card") do
      content_tag(:div, class: "ticket-header") do
        content_tag(:div, form_title(record), class: "ticket-id") +
        content_tag(:div, class: "terminal-command") do
          content_tag(:span, "#{controller_name}$", class: "terminal-prompt") +
          " #{form_action_text(record)}"
        end
      end +
      content_tag(:div, class: "ticket-content") do
        form_for(record, options, &block)
      end
    end
  end

  def form_title(record)
    if record.is_a?(Array)
      main_record = record.last
      action = main_record.new_record? ? "New" : "Edit"
      "#{action} #{main_record.class.name}"
    else
      action = record.new_record? ? "New" : "Edit"
      "#{action} #{record.class.name}"
    end
  end

  def form_action_text(record)
    if record.is_a?(Array)
      main_record = record.last
      action = main_record.new_record? ? "new" : "edit"
      "#{action} #{main_record.class.name.underscore.gsub('_', '-')}"
    else
      action = record.new_record? ? "new" : "edit"
      "#{action} #{record.class.name.underscore.gsub('_', '-')}"
    end
  end

  def terminal_submit_button(form, text = nil)
    text ||= form.object.new_record? ? "Create" : "Update"
    form.submit text, class: "terminal-btn terminal-btn-primary"
  end

  def terminal_back_button(path, text = "Back")
    link_to text, path, class: "terminal-btn"
  end

  def terminal_delete_button(record, text = "Delete")
    button_to text, record,
      method: :delete,
      data: { turbo_confirm: "Are you sure?" },
      class: "terminal-btn terminal-btn-danger"
  end

  def terminal_box(title = nil, &block)
    content_tag(:div, class: "modern-ticket-card") do
      if title
        content_tag(:div, class: "ticket-header") do
          content_tag(:div, title, class: "ticket-id") +
          content_tag(:div, class: "terminal-command") do
            content_tag(:span, "#{controller_name}$", class: "terminal-prompt") +
            " #{title.downcase}"
          end
        end +
        content_tag(:div, class: "ticket-content") do
          capture(&block)
        end
      else
        content_tag(:div, class: "ticket-content") do
          capture(&block)
        end
      end
    end
  end

  def modern_card(title, command = nil, &block)
    content_tag(:div, class: "modern-ticket-card") do
      content_tag(:div, class: "ticket-header") do
        content_tag(:div, title, class: "ticket-id") +
        if command
          content_tag(:div, class: "terminal-command") do
            content_tag(:span, "#{controller_name}$", class: "terminal-prompt") +
            " #{command}"
          end
        else
          ""
        end
      end +
      content_tag(:div, class: "ticket-content") do
        capture(&block)
      end
    end
  end

  def modern_table(&block)
    content_tag(:div, class: "modern-table-container") do
      content_tag(:table, class: "modern-table") do
        capture(&block)
      end
    end
  end
end
